"""
K8s Manager Platform - 主启动文件
"""
import os
from flask.cli import with_appcontext
import click
from app import create_app, db
from app.models import User, Role, Permission, Cluster


app = create_app(os.getenv('FLASK_ENV') or 'default')


@app.shell_context_processor
def make_shell_context():
    """Shell 上下文处理器"""
    return {
        'db': db,
        'User': User,
        'Role': Role,
        'Permission': Permission,
        'Cluster': Cluster
    }


@app.cli.command()
@with_appcontext
def init_db():
    """初始化数据库"""
    db.create_all()
    click.echo('Database initialized.')


@app.cli.command()
@with_appcontext
def init_data():
    """初始化基础数据"""
    # 创建默认权限
    permissions = [
        # 集群管理权限
        ('cluster.create', '创建集群', 'cluster', 'create'),
        ('cluster.read', '查看集群', 'cluster', 'read'),
        ('cluster.update', '更新集群', 'cluster', 'update'),
        ('cluster.delete', '删除集群', 'cluster', 'delete'),
        
        # 应用管理权限
        ('application.create', '创建应用', 'application', 'create'),
        ('application.read', '查看应用', 'application', 'read'),
        ('application.update', '更新应用', 'application', 'update'),
        ('application.delete', '删除应用', 'application', 'delete'),
        ('application.deploy', '部署应用', 'application', 'deploy'),
        
        # 用户管理权限
        ('user.create', '创建用户', 'user', 'create'),
        ('user.read', '查看用户', 'user', 'read'),
        ('user.update', '更新用户', 'user', 'update'),
        ('user.delete', '删除用户', 'user', 'delete'),
        
        # 角色管理权限
        ('role.create', '创建角色', 'role', 'create'),
        ('role.read', '查看角色', 'role', 'read'),
        ('role.update', '更新角色', 'role', 'update'),
        ('role.delete', '删除角色', 'role', 'delete'),
        
        # 监控权限
        ('monitoring.read', '查看监控', 'monitoring', 'read'),
        ('monitoring.manage', '管理监控', 'monitoring', 'manage'),
        
        # 安全权限
        ('security.read', '查看安全', 'security', 'read'),
        ('security.manage', '管理安全', 'security', 'manage'),
        
        # 资源管理权限
        ('resource.read', '查看资源', 'resource', 'read'),
        ('resource.manage', '管理资源', 'resource', 'manage'),
    ]
    
    for name, description, resource, action in permissions:
        if not Permission.query.filter_by(name=name).first():
            permission = Permission(
                name=name,
                description=description,
                resource=resource,
                action=action
            )
            db.session.add(permission)
    
    # 创建默认角色
    roles_data = [
        {
            'name': 'admin',
            'description': '系统管理员',
            'is_system': True,
            'permissions': [p[0] for p in permissions]  # 所有权限
        },
        {
            'name': 'cluster_admin',
            'description': '集群管理员',
            'is_system': True,
            'permissions': [
                'cluster.create', 'cluster.read', 'cluster.update', 'cluster.delete',
                'application.create', 'application.read', 'application.update', 'application.delete', 'application.deploy',
                'monitoring.read', 'monitoring.manage',
                'security.read', 'security.manage',
                'resource.read', 'resource.manage'
            ]
        },
        {
            'name': 'developer',
            'description': '开发者',
            'is_system': True,
            'permissions': [
                'cluster.read',
                'application.create', 'application.read', 'application.update', 'application.deploy',
                'monitoring.read',
                'security.read',
                'resource.read'
            ]
        },
        {
            'name': 'viewer',
            'description': '只读用户',
            'is_system': True,
            'permissions': [
                'cluster.read',
                'application.read',
                'monitoring.read',
                'security.read',
                'resource.read'
            ]
        }
    ]
    
    for role_data in roles_data:
        if not Role.query.filter_by(name=role_data['name']).first():
            role = Role(
                name=role_data['name'],
                description=role_data['description'],
                is_system=role_data['is_system']
            )
            
            # 添加权限
            for perm_name in role_data['permissions']:
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission:
                    role.permissions.append(permission)
            
            db.session.add(role)
    
    # 创建默认管理员用户
    if not User.query.filter_by(username='admin').first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            full_name='系统管理员',
            is_admin=True
        )
        admin_user.set_password('admin123')
        
        if admin_role:
            admin_user.roles.append(admin_role)
        
        db.session.add(admin_user)
    
    db.session.commit()
    click.echo('Initial data created.')


@app.cli.command()
@with_appcontext
def reset_db():
    """重置数据库"""
    db.drop_all()
    db.create_all()
    click.echo('Database reset.')


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

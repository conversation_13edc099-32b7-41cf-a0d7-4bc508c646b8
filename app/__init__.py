"""
K8s Manager Platform - Flask Application Factory
"""
import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_socketio import SocketIO

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
jwt = JWTManager()
socketio = SocketIO()


def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    from app.config import config
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    jwt.init_app(app)
    socketio.init_app(app, cors_allowed_origins="*")
    
    # 配置 CORS
    CORS(app)
    
    # 配置登录管理器
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'
    
    # 注册蓝图
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api/v1')
    
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.cluster import bp as cluster_bp
    app.register_blueprint(cluster_bp, url_prefix='/cluster')
    
    from app.application import bp as app_bp
    app.register_blueprint(app_bp, url_prefix='/application')
    
    from app.monitoring import bp as monitoring_bp
    app.register_blueprint(monitoring_bp, url_prefix='/monitoring')
    
    from app.security import bp as security_bp
    app.register_blueprint(security_bp, url_prefix='/security')
    
    from app.resource import bp as resource_bp
    app.register_blueprint(resource_bp, url_prefix='/resource')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    # 错误处理
    from app.errors import bp as errors_bp
    app.register_blueprint(errors_bp)
    
    return app


# 导入模型以确保它们被注册
from app.models import user, cluster, application, monitoring, audit, resource, security

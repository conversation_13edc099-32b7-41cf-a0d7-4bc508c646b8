{% extends "base.html" %}

{% block title %}仪表板 - K8s Manager Platform{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>仪表板</h1>
    <button class="btn btn-primary" onclick="refreshDashboard()">
        <i class="fas fa-sync-alt"></i> 刷新
    </button>
</div>

<!-- 概览卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-metric">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">集群总数</h6>
                        <h3 class="mb-0" id="cluster-count">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-metric success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">应用总数</h6>
                        <h3 class="mb-0" id="app-count">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cube fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-metric warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">活跃告警</h6>
                        <h3 class="mb-0" id="alert-count">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-metric danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">安全扫描</h6>
                        <h3 class="mb-0" id="scan-count">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shield-alt fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">集群资源使用趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="resource-chart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">集群状态分布</h5>
            </div>
            <div class="card-body">
                <canvas id="cluster-status-chart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最近告警</h5>
            </div>
            <div class="card-body" id="recent-alerts">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最近部署</h5>
            </div>
            <div class="card-body" id="recent-deployments">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let resourceChart;
let clusterStatusChart;

// 初始化仪表板
document.addEventListener('DOMContentLoaded', function() {
    initCharts();
    loadDashboardData();
});

// 初始化图表
function initCharts() {
    // 资源使用趋势图
    const resourceCtx = document.getElementById('resource-chart').getContext('2d');
    resourceChart = new Chart(resourceCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'CPU 使用率 (%)',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }, {
                label: '内存使用率 (%)',
                data: [],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
    
    // 集群状态分布图
    const clusterCtx = document.getElementById('cluster-status-chart').getContext('2d');
    clusterStatusChart = new Chart(clusterCtx, {
        type: 'doughnut',
        data: {
            labels: ['健康', '不健康', '未知'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    'rgb(40, 167, 69)',
                    'rgb(220, 53, 69)',
                    'rgb(108, 117, 125)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        // 加载概览数据
        await loadOverviewData();
        
        // 加载图表数据
        await loadChartData();
        
        // 加载最近活动
        await loadRecentAlerts();
        await loadRecentDeployments();
        
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
    }
}

// 加载概览数据
async function loadOverviewData() {
    try {
        // 获取集群数量
        const clustersResponse = await apiRequest('/clusters');
        if (clustersResponse.ok) {
            const clustersData = await clustersResponse.json();
            document.getElementById('cluster-count').textContent = clustersData.total || 0;
        }
        
        // 获取应用数量
        const appsResponse = await apiRequest('/applications');
        if (appsResponse.ok) {
            const appsData = await appsResponse.json();
            document.getElementById('app-count').textContent = appsData.total || 0;
        }
        
        // 获取告警数量
        const alertsResponse = await apiRequest('/monitoring/alerts?status=firing');
        if (alertsResponse.ok) {
            const alertsData = await alertsResponse.json();
            document.getElementById('alert-count').textContent = alertsData.total || 0;
        }
        
        // 获取安全扫描数量
        const scansResponse = await apiRequest('/security/scans?limit=10');
        if (scansResponse.ok) {
            const scansData = await scansResponse.json();
            document.getElementById('scan-count').textContent = scansData.total || 0;
        }
        
    } catch (error) {
        console.error('Failed to load overview data:', error);
    }
}

// 加载图表数据
async function loadChartData() {
    try {
        // 模拟资源使用数据
        const now = new Date();
        const labels = [];
        const cpuData = [];
        const memoryData = [];
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            labels.push(time.getHours() + ':00');
            cpuData.push(Math.random() * 80 + 10);
            memoryData.push(Math.random() * 70 + 15);
        }
        
        resourceChart.data.labels = labels;
        resourceChart.data.datasets[0].data = cpuData;
        resourceChart.data.datasets[1].data = memoryData;
        resourceChart.update();
        
        // 更新集群状态分布
        clusterStatusChart.data.datasets[0].data = [3, 1, 0];
        clusterStatusChart.update();
        
    } catch (error) {
        console.error('Failed to load chart data:', error);
    }
}

// 加载最近告警
async function loadRecentAlerts() {
    const alertsContainer = document.getElementById('recent-alerts');
    
    try {
        const response = await apiRequest('/monitoring/alerts?limit=5');
        if (response.ok) {
            const data = await response.json();
            const alerts = data.alerts || [];
            
            if (alerts.length === 0) {
                alertsContainer.innerHTML = '<p class="text-muted">暂无告警</p>';
                return;
            }
            
            let html = '';
            alerts.forEach(alert => {
                const severityClass = {
                    'critical': 'danger',
                    'warning': 'warning',
                    'info': 'info'
                }[alert.severity] || 'secondary';
                
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="badge bg-${severityClass}">${alert.severity}</span>
                            <span class="ms-2">${alert.name}</span>
                        </div>
                        <small class="text-muted">${formatTime(alert.starts_at)}</small>
                    </div>
                `;
            });
            
            alertsContainer.innerHTML = html;
        } else {
            showError(alertsContainer, '加载告警数据失败');
        }
    } catch (error) {
        showError(alertsContainer, '加载告警数据失败');
    }
}

// 加载最近部署
async function loadRecentDeployments() {
    const deploymentsContainer = document.getElementById('recent-deployments');
    
    try {
        // 模拟部署数据
        const deployments = [
            { name: 'web-app', status: 'success', time: new Date().toISOString() },
            { name: 'api-service', status: 'success', time: new Date(Date.now() - 3600000).toISOString() },
            { name: 'worker', status: 'failed', time: new Date(Date.now() - 7200000).toISOString() }
        ];
        
        if (deployments.length === 0) {
            deploymentsContainer.innerHTML = '<p class="text-muted">暂无部署记录</p>';
            return;
        }
        
        let html = '';
        deployments.forEach(deployment => {
            const statusClass = deployment.status === 'success' ? 'success' : 'danger';
            const statusIcon = deployment.status === 'success' ? 'check' : 'times';
            
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <i class="fas fa-${statusIcon} text-${statusClass} me-2"></i>
                        <span>${deployment.name}</span>
                    </div>
                    <small class="text-muted">${formatTime(deployment.time)}</small>
                </div>
            `;
        });
        
        deploymentsContainer.innerHTML = html;
        
    } catch (error) {
        showError(deploymentsContainer, '加载部署数据失败');
    }
}

// 刷新仪表板
function refreshDashboard() {
    loadDashboardData();
}
</script>
{% endblock %}

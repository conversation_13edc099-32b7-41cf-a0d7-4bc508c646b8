<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}K8s Manager Platform{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .card-metric {
            border-left: 4px solid #007bff;
        }
        .card-metric.warning {
            border-left-color: #ffc107;
        }
        .card-metric.danger {
            border-left-color: #dc3545;
        }
        .card-metric.success {
            border-left-color: #28a745;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar position-fixed d-none d-md-block">
        <div class="p-3">
            <h4 class="text-white">K8s Manager</h4>
            <hr class="text-white">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        仪表板
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/clusters">
                        <i class="fas fa-server me-2"></i>
                        集群管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/applications">
                        <i class="fas fa-cube me-2"></i>
                        应用管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/monitoring">
                        <i class="fas fa-chart-line me-2"></i>
                        监控告警
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/security">
                        <i class="fas fa-shield-alt me-2"></i>
                        安全管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/resources">
                        <i class="fas fa-cogs me-2"></i>
                        资源管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/users">
                        <i class="fas fa-users me-2"></i>
                        用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/audit">
                        <i class="fas fa-history me-2"></i>
                        审计日志
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            用户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/settings">设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/auth/logout">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-fluid p-4">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义 JavaScript -->
    <script>
        // API 基础 URL
        const API_BASE_URL = '/api/v1';
        
        // 通用 API 请求函数
        async function apiRequest(url, options = {}) {
            const token = localStorage.getItem('access_token');
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': token ? `Bearer ${token}` : ''
                }
            };
            
            const response = await fetch(API_BASE_URL + url, {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            });
            
            if (response.status === 401) {
                // Token 过期，重定向到登录页
                window.location.href = '/auth/login';
                return;
            }
            
            return response;
        }
        
        // 格式化字节数
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
        
        // 格式化时间
        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleString('zh-CN');
        }
        
        // 显示加载状态
        function showLoading(element) {
            element.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
        }
        
        // 显示错误信息
        function showError(element, message) {
            element.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>

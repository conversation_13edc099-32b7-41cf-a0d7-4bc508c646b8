"""
主页路由
"""
from flask import render_template, jsonify, redirect, url_for
from flask_login import login_required
from app.main import bp


@bp.route('/')
def index():
    """主页"""
    return redirect(url_for('main.dashboard'))


@bp.route('/dashboard')
@login_required
def dashboard():
    """仪表板"""
    return render_template('dashboard.html')


@bp.route('/health')
def health():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': '2024-01-01T00:00:00Z'
    })


@bp.route('/api/docs')
def api_docs():
    """API 文档"""
    return jsonify({
        'message': 'API Documentation',
        'swagger_url': '/api/v1/swagger',
        'endpoints': {
            'auth': '/auth',
            'clusters': '/api/v1/clusters',
            'applications': '/api/v1/applications',
            'monitoring': '/api/v1/monitoring',
            'security': '/api/v1/security',
            'resources': '/api/v1/resources'
        }
    })

"""
集群管理路由
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.cluster import bp
from app.models.cluster import Cluster
from app.models.audit import AuditLog
from app.services.kubernetes_service import KubernetesService
from app import db


@bp.route('/test', methods=['GET'])
@jwt_required()
def test_cluster():
    """测试集群连接"""
    kubeconfig = request.args.get('kubeconfig')
    
    try:
        k8s_service = KubernetesService()
        result = k8s_service.test_connection(kubeconfig)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500


@bp.route('/info', methods=['GET'])
@jwt_required()
def cluster_info():
    """获取集群信息"""
    cluster_name = request.args.get('cluster_name')
    kubeconfig = request.args.get('kubeconfig')
    
    try:
        k8s_service = KubernetesService()
        info = k8s_service.get_cluster_info(cluster_name, kubeconfig)
        
        return jsonify({
            'status': 'success',
            'data': info
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500


@bp.route('/nodes', methods=['GET'])
@jwt_required()
def get_nodes():
    """获取集群节点"""
    cluster_name = request.args.get('cluster_name')
    kubeconfig = request.args.get('kubeconfig')
    
    try:
        k8s_service = KubernetesService()
        nodes = k8s_service.get_nodes(cluster_name, kubeconfig)
        
        return jsonify({
            'status': 'success',
            'data': nodes
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500


@bp.route('/namespaces', methods=['GET'])
@jwt_required()
def get_namespaces():
    """获取集群命名空间"""
    cluster_name = request.args.get('cluster_name')
    kubeconfig = request.args.get('kubeconfig')
    
    try:
        k8s_service = KubernetesService()
        namespaces = k8s_service.get_namespaces(cluster_name, kubeconfig)
        
        return jsonify({
            'status': 'success',
            'data': namespaces
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

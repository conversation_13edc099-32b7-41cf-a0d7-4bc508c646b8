"""
应用管理服务
"""
import yaml
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import current_app
from kubernetes import client
from kubernetes.client.rest import ApiException
from app import db
from app.models.application import Application, Deployment, Service
from app.models.cluster import Cluster
from app.models.audit import AuditLog
from app.services.kubernetes_service import KubernetesService


class ApplicationService:
    """应用管理服务类"""
    
    def __init__(self):
        self.k8s_service = KubernetesService()
    
    def deploy_application(self, app_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """部署应用"""
        try:
            # 验证集群存在
            cluster = Cluster.query.get(app_data['cluster_id'])
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            # 创建应用记录
            application = Application(
                name=app_data['name'],
                display_name=app_data.get('display_name'),
                description=app_data.get('description'),
                app_type=app_data.get('app_type', 'deployment'),
                namespace=app_data['namespace'],
                cluster_id=app_data['cluster_id'],
                image=app_data['image'],
                image_tag=app_data.get('image_tag', 'latest'),
                replicas=app_data.get('replicas', 1),
                cpu_request=app_data.get('cpu_request'),
                memory_request=app_data.get('memory_request'),
                cpu_limit=app_data.get('cpu_limit'),
                memory_limit=app_data.get('memory_limit'),
                env_vars=app_data.get('env_vars'),
                ports=app_data.get('ports'),
                labels=app_data.get('labels'),
                deployed_by=user_id,
                deployment_method=app_data.get('deployment_method', 'gui')
            )
            
            db.session.add(application)
            db.session.flush()  # 获取 application.id
            
            # 部署到 Kubernetes
            if app_data.get('deployment_method') == 'yaml':
                result = self._deploy_from_yaml(application, app_data.get('yaml_content'), cluster)
            else:
                result = self._deploy_from_config(application, app_data, cluster)
            
            if result['success']:
                application.status = 'running'
                application.deployed_at = datetime.utcnow()
                
                # 创建部署记录
                deployment = Deployment(
                    application_id=application.id,
                    version=app_data.get('version', '1.0.0'),
                    revision=1,
                    status='complete',
                    deployment_config=app_data,
                    strategy=app_data.get('strategy', 'rolling'),
                    deployed_by=user_id,
                    completed_at=datetime.utcnow()
                )
                db.session.add(deployment)
                
                db.session.commit()
                
                # 记录审计日志
                AuditLog.log_action(
                    user_id=user_id,
                    action='deploy',
                    resource_type='application',
                    resource_id=str(application.id),
                    resource_name=application.name,
                    cluster_id=cluster.id,
                    namespace=application.namespace,
                    status='success',
                    new_values=application.to_dict()
                )
                
                return {
                    'success': True,
                    'application': application.to_dict(),
                    'message': '应用部署成功'
                }
            else:
                db.session.rollback()
                return result
                
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to deploy application: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_application(self, app_id: int, app_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """更新应用"""
        try:
            application = Application.query.get(app_id)
            if not application:
                return {
                    'success': False,
                    'error': 'Application not found'
                }
            
            cluster = Cluster.query.get(application.cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            # 保存旧值用于审计
            old_values = application.to_dict()
            
            # 更新应用配置
            updatable_fields = [
                'display_name', 'description', 'image', 'image_tag', 'replicas',
                'cpu_request', 'memory_request', 'cpu_limit', 'memory_limit',
                'env_vars', 'ports', 'labels'
            ]
            
            for field in updatable_fields:
                if field in app_data:
                    setattr(application, field, app_data[field])
            
            # 更新 Kubernetes 资源
            result = self._update_kubernetes_deployment(application, cluster)
            
            if result['success']:
                # 创建新的部署记录
                last_deployment = Deployment.query.filter_by(
                    application_id=application.id
                ).order_by(Deployment.revision.desc()).first()
                
                new_revision = (last_deployment.revision + 1) if last_deployment else 1
                
                deployment = Deployment(
                    application_id=application.id,
                    version=app_data.get('version', '1.0.0'),
                    revision=new_revision,
                    status='complete',
                    deployment_config=app_data,
                    changes=self._calculate_changes(old_values, application.to_dict()),
                    strategy=app_data.get('strategy', 'rolling'),
                    deployed_by=user_id,
                    completed_at=datetime.utcnow()
                )
                db.session.add(deployment)
                
                db.session.commit()
                
                # 记录审计日志
                AuditLog.log_action(
                    user_id=user_id,
                    action='update',
                    resource_type='application',
                    resource_id=str(application.id),
                    resource_name=application.name,
                    cluster_id=cluster.id,
                    namespace=application.namespace,
                    status='success',
                    old_values=old_values,
                    new_values=application.to_dict()
                )
                
                return {
                    'success': True,
                    'application': application.to_dict(),
                    'message': '应用更新成功'
                }
            else:
                return result
                
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to update application: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def scale_application(self, app_id: int, replicas: int, user_id: int) -> Dict[str, Any]:
        """扩缩容应用"""
        try:
            application = Application.query.get(app_id)
            if not application:
                return {
                    'success': False,
                    'error': 'Application not found'
                }
            
            cluster = Cluster.query.get(application.cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            old_replicas = application.replicas
            
            # 更新 Kubernetes Deployment
            apps_v1 = self.k8s_service.get_apps_v1_api(cluster.name, cluster.kubeconfig)
            
            try:
                # 获取当前 Deployment
                deployment = apps_v1.read_namespaced_deployment(
                    name=application.name,
                    namespace=application.namespace
                )
                
                # 更新副本数
                deployment.spec.replicas = replicas
                
                # 应用更新
                apps_v1.patch_namespaced_deployment(
                    name=application.name,
                    namespace=application.namespace,
                    body=deployment
                )
                
                # 更新数据库记录
                application.replicas = replicas
                db.session.commit()
                
                # 记录审计日志
                AuditLog.log_action(
                    user_id=user_id,
                    action='scale',
                    resource_type='application',
                    resource_id=str(application.id),
                    resource_name=application.name,
                    cluster_id=cluster.id,
                    namespace=application.namespace,
                    status='success',
                    changes={
                        'replicas': {
                            'old': old_replicas,
                            'new': replicas
                        }
                    }
                )
                
                return {
                    'success': True,
                    'application': application.to_dict(),
                    'message': f'应用扩缩容成功：{old_replicas} -> {replicas}'
                }
                
            except ApiException as e:
                return {
                    'success': False,
                    'error': f'Kubernetes API error: {e.reason}'
                }
                
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to scale application: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_application(self, app_id: int, user_id: int) -> Dict[str, Any]:
        """删除应用"""
        try:
            application = Application.query.get(app_id)
            if not application:
                return {
                    'success': False,
                    'error': 'Application not found'
                }
            
            cluster = Cluster.query.get(application.cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            app_data = application.to_dict()
            
            # 从 Kubernetes 删除资源
            result = self._delete_kubernetes_resources(application, cluster)
            
            if result['success']:
                # 删除数据库记录
                db.session.delete(application)
                db.session.commit()
                
                # 记录审计日志
                AuditLog.log_action(
                    user_id=user_id,
                    action='delete',
                    resource_type='application',
                    resource_id=str(app_id),
                    resource_name=app_data['name'],
                    cluster_id=cluster.id,
                    namespace=app_data['namespace'],
                    status='success',
                    old_values=app_data
                )
                
                return {
                    'success': True,
                    'message': '应用删除成功'
                }
            else:
                return result
                
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to delete application: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _deploy_from_config(self, application: Application, app_data: Dict, cluster: Cluster) -> Dict[str, Any]:
        """从配置部署应用"""
        try:
            apps_v1 = self.k8s_service.get_apps_v1_api(cluster.name, cluster.kubeconfig)
            core_v1 = self.k8s_service.get_core_v1_api(cluster.name, cluster.kubeconfig)
            
            # 创建 Deployment
            deployment_manifest = self._create_deployment_manifest(application, app_data)
            
            apps_v1.create_namespaced_deployment(
                namespace=application.namespace,
                body=deployment_manifest
            )
            
            # 如果有端口配置，创建 Service
            if app_data.get('ports'):
                service_manifest = self._create_service_manifest(application, app_data)
                core_v1.create_namespaced_service(
                    namespace=application.namespace,
                    body=service_manifest
                )
            
            return {
                'success': True,
                'message': 'Application deployed successfully'
            }
            
        except ApiException as e:
            return {
                'success': False,
                'error': f'Kubernetes API error: {e.reason}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _deploy_from_yaml(self, application: Application, yaml_content: str, cluster: Cluster) -> Dict[str, Any]:
        """从 YAML 部署应用"""
        try:
            # 解析 YAML
            manifests = list(yaml.safe_load_all(yaml_content))
            
            api_client = self.k8s_service.get_client(cluster.name, cluster.kubeconfig)
            
            for manifest in manifests:
                if not manifest:
                    continue
                
                # 根据资源类型选择合适的 API
                kind = manifest.get('kind')
                api_version = manifest.get('apiVersion')
                
                if kind == 'Deployment' and api_version.startswith('apps/'):
                    apps_v1 = self.k8s_service.get_apps_v1_api(cluster.name, cluster.kubeconfig)
                    apps_v1.create_namespaced_deployment(
                        namespace=application.namespace,
                        body=manifest
                    )
                elif kind == 'Service' and api_version == 'v1':
                    core_v1 = self.k8s_service.get_core_v1_api(cluster.name, cluster.kubeconfig)
                    core_v1.create_namespaced_service(
                        namespace=application.namespace,
                        body=manifest
                    )
                # 可以扩展支持更多资源类型
            
            return {
                'success': True,
                'message': 'Application deployed from YAML successfully'
            }
            
        except yaml.YAMLError as e:
            return {
                'success': False,
                'error': f'YAML parsing error: {str(e)}'
            }
        except ApiException as e:
            return {
                'success': False,
                'error': f'Kubernetes API error: {e.reason}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_deployment_manifest(self, application: Application, app_data: Dict) -> Dict:
        """创建 Deployment 清单"""
        container_spec = {
            'name': application.name,
            'image': f"{application.image}:{application.image_tag}",
            'imagePullPolicy': application.image_pull_policy or 'IfNotPresent'
        }
        
        # 添加资源限制
        resources = {}
        if application.cpu_request or application.memory_request:
            resources['requests'] = {}
            if application.cpu_request:
                resources['requests']['cpu'] = application.cpu_request
            if application.memory_request:
                resources['requests']['memory'] = application.memory_request
        
        if application.cpu_limit or application.memory_limit:
            resources['limits'] = {}
            if application.cpu_limit:
                resources['limits']['cpu'] = application.cpu_limit
            if application.memory_limit:
                resources['limits']['memory'] = application.memory_limit
        
        if resources:
            container_spec['resources'] = resources
        
        # 添加环境变量
        if application.env_vars:
            container_spec['env'] = [
                {'name': k, 'value': str(v)} for k, v in application.env_vars.items()
            ]
        
        # 添加端口
        if application.ports:
            container_spec['ports'] = [
                {'containerPort': port['container_port'], 'name': port.get('name', 'http')}
                for port in application.ports
            ]
        
        deployment_manifest = {
            'apiVersion': 'apps/v1',
            'kind': 'Deployment',
            'metadata': {
                'name': application.name,
                'namespace': application.namespace,
                'labels': application.labels or {}
            },
            'spec': {
                'replicas': application.replicas,
                'selector': {
                    'matchLabels': {'app': application.name}
                },
                'template': {
                    'metadata': {
                        'labels': {'app': application.name}
                    },
                    'spec': {
                        'containers': [container_spec]
                    }
                }
            }
        }
        
        return deployment_manifest
    
    def _create_service_manifest(self, application: Application, app_data: Dict) -> Dict:
        """创建 Service 清单"""
        service_manifest = {
            'apiVersion': 'v1',
            'kind': 'Service',
            'metadata': {
                'name': f"{application.name}-service",
                'namespace': application.namespace,
                'labels': application.labels or {}
            },
            'spec': {
                'selector': {'app': application.name},
                'ports': [
                    {
                        'port': port.get('service_port', port['container_port']),
                        'targetPort': port['container_port'],
                        'name': port.get('name', 'http')
                    }
                    for port in application.ports
                ],
                'type': application.service_type or 'ClusterIP'
            }
        }
        
        return service_manifest
    
    def _update_kubernetes_deployment(self, application: Application, cluster: Cluster) -> Dict[str, Any]:
        """更新 Kubernetes Deployment"""
        try:
            apps_v1 = self.k8s_service.get_apps_v1_api(cluster.name, cluster.kubeconfig)
            
            # 获取当前 Deployment
            deployment = apps_v1.read_namespaced_deployment(
                name=application.name,
                namespace=application.namespace
            )
            
            # 更新镜像
            container = deployment.spec.template.spec.containers[0]
            container.image = f"{application.image}:{application.image_tag}"
            
            # 更新副本数
            deployment.spec.replicas = application.replicas
            
            # 应用更新
            apps_v1.patch_namespaced_deployment(
                name=application.name,
                namespace=application.namespace,
                body=deployment
            )
            
            return {
                'success': True,
                'message': 'Kubernetes deployment updated successfully'
            }
            
        except ApiException as e:
            return {
                'success': False,
                'error': f'Kubernetes API error: {e.reason}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _delete_kubernetes_resources(self, application: Application, cluster: Cluster) -> Dict[str, Any]:
        """删除 Kubernetes 资源"""
        try:
            apps_v1 = self.k8s_service.get_apps_v1_api(cluster.name, cluster.kubeconfig)
            core_v1 = self.k8s_service.get_core_v1_api(cluster.name, cluster.kubeconfig)
            
            # 删除 Deployment
            try:
                apps_v1.delete_namespaced_deployment(
                    name=application.name,
                    namespace=application.namespace
                )
            except ApiException as e:
                if e.status != 404:  # 忽略资源不存在的错误
                    raise
            
            # 删除 Service
            try:
                core_v1.delete_namespaced_service(
                    name=f"{application.name}-service",
                    namespace=application.namespace
                )
            except ApiException as e:
                if e.status != 404:  # 忽略资源不存在的错误
                    current_app.logger.warning(f"Service not found: {e.reason}")
            
            return {
                'success': True,
                'message': 'Kubernetes resources deleted successfully'
            }
            
        except ApiException as e:
            return {
                'success': False,
                'error': f'Kubernetes API error: {e.reason}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_changes(self, old_values: Dict, new_values: Dict) -> Dict:
        """计算变更内容"""
        changes = {}
        
        for key, new_value in new_values.items():
            old_value = old_values.get(key)
            if old_value != new_value:
                changes[key] = {
                    'old': old_value,
                    'new': new_value
                }
        
        return changes

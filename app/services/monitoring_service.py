"""
监控服务
"""
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from flask import current_app
from app import db
from app.models.monitoring import Alert, Event, Metric
from app.models.cluster import Cluster
from app.models.audit import AuditLog


class MonitoringService:
    """监控服务类"""
    
    def __init__(self):
        self.prometheus_url = current_app.config.get('PROMETHEUS_URL')
        self.grafana_url = current_app.config.get('GRAFANA_URL')
        self.elasticsearch_url = current_app.config.get('ELASTICSEARCH_URL')
    
    def get_cluster_metrics(self, cluster_id: int, time_range: str = '1h') -> Dict[str, Any]:
        """获取集群指标"""
        try:
            cluster = Cluster.query.get(cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            if not self.prometheus_url:
                return {
                    'success': False,
                    'error': 'Prometheus URL not configured'
                }
            
            # 构建 Prometheus 查询
            queries = {
                'cpu_usage': f'100 - (avg(irate(node_cpu_seconds_total{{mode="idle",cluster="{cluster.name}"}}[5m])) * 100)',
                'memory_usage': f'(1 - (node_memory_MemAvailable_bytes{{cluster="{cluster.name}"}} / node_memory_MemTotal_bytes{{cluster="{cluster.name}"}})) * 100',
                'disk_usage': f'100 - ((node_filesystem_avail_bytes{{cluster="{cluster.name}",mountpoint="/"}} * 100) / node_filesystem_size_bytes{{cluster="{cluster.name}",mountpoint="/"}})',
                'pod_count': f'sum(kube_pod_info{{cluster="{cluster.name}"}})',
                'node_count': f'count(kube_node_info{{cluster="{cluster.name}"}})',
                'network_receive': f'sum(rate(node_network_receive_bytes_total{{cluster="{cluster.name}"}}[5m]))',
                'network_transmit': f'sum(rate(node_network_transmit_bytes_total{{cluster="{cluster.name}"}}[5m]))'
            }
            
            metrics = {}
            for metric_name, query in queries.items():
                result = self._query_prometheus(query, time_range)
                if result['success']:
                    metrics[metric_name] = result['data']
                else:
                    current_app.logger.warning(f"Failed to query {metric_name}: {result['error']}")
                    metrics[metric_name] = []
            
            return {
                'success': True,
                'cluster_id': cluster_id,
                'cluster_name': cluster.name,
                'time_range': time_range,
                'metrics': metrics,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get cluster metrics: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_node_metrics(self, cluster_id: int, node_name: str, time_range: str = '1h') -> Dict[str, Any]:
        """获取节点指标"""
        try:
            cluster = Cluster.query.get(cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            if not self.prometheus_url:
                return {
                    'success': False,
                    'error': 'Prometheus URL not configured'
                }
            
            # 构建节点级别的 Prometheus 查询
            queries = {
                'cpu_usage': f'100 - (avg(irate(node_cpu_seconds_total{{mode="idle",instance=~"{node_name}.*"}}[5m])) * 100)',
                'memory_usage': f'(1 - (node_memory_MemAvailable_bytes{{instance=~"{node_name}.*"}} / node_memory_MemTotal_bytes{{instance=~"{node_name}.*"}})) * 100',
                'disk_usage': f'100 - ((node_filesystem_avail_bytes{{instance=~"{node_name}.*",mountpoint="/"}} * 100) / node_filesystem_size_bytes{{instance=~"{node_name}.*",mountpoint="/"}})',
                'load_average': f'node_load1{{instance=~"{node_name}.*"}}',
                'network_receive': f'rate(node_network_receive_bytes_total{{instance=~"{node_name}.*"}}[5m])',
                'network_transmit': f'rate(node_network_transmit_bytes_total{{instance=~"{node_name}.*"}}[5m])',
                'pod_count': f'sum(kube_pod_info{{node="{node_name}"}})'
            }
            
            metrics = {}
            for metric_name, query in queries.items():
                result = self._query_prometheus(query, time_range)
                if result['success']:
                    metrics[metric_name] = result['data']
                else:
                    current_app.logger.warning(f"Failed to query {metric_name}: {result['error']}")
                    metrics[metric_name] = []
            
            return {
                'success': True,
                'cluster_id': cluster_id,
                'cluster_name': cluster.name,
                'node_name': node_name,
                'time_range': time_range,
                'metrics': metrics,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get node metrics: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_application_metrics(self, app_id: int, time_range: str = '1h') -> Dict[str, Any]:
        """获取应用指标"""
        try:
            from app.models.application import Application
            
            application = Application.query.get(app_id)
            if not application:
                return {
                    'success': False,
                    'error': 'Application not found'
                }
            
            cluster = Cluster.query.get(application.cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            if not self.prometheus_url:
                return {
                    'success': False,
                    'error': 'Prometheus URL not configured'
                }
            
            # 构建应用级别的 Prometheus 查询
            queries = {
                'cpu_usage': f'sum(rate(container_cpu_usage_seconds_total{{namespace="{application.namespace}",pod=~"{application.name}.*"}}[5m])) * 100',
                'memory_usage': f'sum(container_memory_working_set_bytes{{namespace="{application.namespace}",pod=~"{application.name}.*"}}) / 1024 / 1024',
                'network_receive': f'sum(rate(container_network_receive_bytes_total{{namespace="{application.namespace}",pod=~"{application.name}.*"}}[5m]))',
                'network_transmit': f'sum(rate(container_network_transmit_bytes_total{{namespace="{application.namespace}",pod=~"{application.name}.*"}}[5m]))',
                'pod_count': f'count(kube_pod_info{{namespace="{application.namespace}",created_by_name="{application.name}"}})',
                'restart_count': f'sum(kube_pod_container_status_restarts_total{{namespace="{application.namespace}",pod=~"{application.name}.*"}})',
                'request_rate': f'sum(rate(http_requests_total{{namespace="{application.namespace}",pod=~"{application.name}.*"}}[5m]))',
                'error_rate': f'sum(rate(http_requests_total{{namespace="{application.namespace}",pod=~"{application.name}.*",status=~"5.."}}[5m]))'
            }
            
            metrics = {}
            for metric_name, query in queries.items():
                result = self._query_prometheus(query, time_range)
                if result['success']:
                    metrics[metric_name] = result['data']
                else:
                    current_app.logger.warning(f"Failed to query {metric_name}: {result['error']}")
                    metrics[metric_name] = []
            
            return {
                'success': True,
                'application_id': app_id,
                'application_name': application.name,
                'namespace': application.namespace,
                'cluster_name': cluster.name,
                'time_range': time_range,
                'metrics': metrics,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get application metrics: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_alerts(self, cluster_id: Optional[int] = None, severity: Optional[str] = None, 
                   status: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
        """获取告警列表"""
        try:
            query = Alert.query
            
            if cluster_id:
                query = query.filter_by(cluster_id=cluster_id)
            if severity:
                query = query.filter_by(severity=severity)
            if status:
                query = query.filter_by(status=status)
            
            alerts = query.order_by(Alert.starts_at.desc()).limit(limit).all()
            
            return {
                'success': True,
                'alerts': [alert.to_dict() for alert in alerts],
                'total': len(alerts)
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get alerts: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def acknowledge_alert(self, alert_id: int, user_id: int, note: str = None) -> Dict[str, Any]:
        """确认告警"""
        try:
            alert = Alert.query.get(alert_id)
            if not alert:
                return {
                    'success': False,
                    'error': 'Alert not found'
                }
            
            alert.acknowledged_by = user_id
            alert.acknowledged_at = datetime.utcnow()
            if note:
                alert.resolution_note = note
            
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=user_id,
                action='acknowledge',
                resource_type='alert',
                resource_id=str(alert.id),
                resource_name=alert.name,
                cluster_id=alert.cluster_id,
                status='success',
                metadata={'note': note}
            )
            
            return {
                'success': True,
                'alert': alert.to_dict(),
                'message': 'Alert acknowledged successfully'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to acknowledge alert: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def resolve_alert(self, alert_id: int, user_id: int, note: str = None) -> Dict[str, Any]:
        """解决告警"""
        try:
            alert = Alert.query.get(alert_id)
            if not alert:
                return {
                    'success': False,
                    'error': 'Alert not found'
                }
            
            alert.status = 'resolved'
            alert.resolved_at = datetime.utcnow()
            alert.ends_at = datetime.utcnow()
            if note:
                alert.resolution_note = note
            
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=user_id,
                action='resolve',
                resource_type='alert',
                resource_id=str(alert.id),
                resource_name=alert.name,
                cluster_id=alert.cluster_id,
                status='success',
                metadata={'note': note}
            )
            
            return {
                'success': True,
                'alert': alert.to_dict(),
                'message': 'Alert resolved successfully'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to resolve alert: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_events(self, cluster_id: Optional[int] = None, namespace: Optional[str] = None,
                   event_type: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
        """获取事件列表"""
        try:
            query = Event.query
            
            if cluster_id:
                query = query.filter_by(cluster_id=cluster_id)
            if namespace:
                query = query.filter_by(namespace=namespace)
            if event_type:
                query = query.filter_by(event_type=event_type)
            
            events = query.order_by(Event.last_timestamp.desc()).limit(limit).all()
            
            return {
                'success': True,
                'events': [event.to_dict() for event in events],
                'total': len(events)
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get events: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _query_prometheus(self, query: str, time_range: str = '1h') -> Dict[str, Any]:
        """查询 Prometheus"""
        try:
            # 计算时间范围
            end_time = datetime.utcnow()
            if time_range == '1h':
                start_time = end_time - timedelta(hours=1)
                step = '1m'
            elif time_range == '6h':
                start_time = end_time - timedelta(hours=6)
                step = '5m'
            elif time_range == '24h':
                start_time = end_time - timedelta(hours=24)
                step = '15m'
            elif time_range == '7d':
                start_time = end_time - timedelta(days=7)
                step = '1h'
            else:
                start_time = end_time - timedelta(hours=1)
                step = '1m'
            
            # 构建查询参数
            params = {
                'query': query,
                'start': start_time.timestamp(),
                'end': end_time.timestamp(),
                'step': step
            }
            
            # 发送请求
            response = requests.get(
                f"{self.prometheus_url}/api/v1/query_range",
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'success':
                    return {
                        'success': True,
                        'data': data['data']['result']
                    }
                else:
                    return {
                        'success': False,
                        'error': data.get('error', 'Unknown Prometheus error')
                    }
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: {response.text}'
                }
                
        except requests.RequestException as e:
            return {
                'success': False,
                'error': f'Request failed: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

"""
集群管理服务
"""
import base64
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from flask import current_app
from sqlalchemy import and_
from app import db
from app.models.cluster import Cluster, ClusterNode, ClusterComponent
from app.models.audit import AuditLog
from app.services.kubernetes_service import KubernetesService


class ClusterService:
    """集群管理服务类"""
    
    def __init__(self):
        self.k8s_service = KubernetesService()
    
    def register_cluster(self, cluster_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """注册新集群"""
        try:
            # 验证集群连接
            test_result = self.k8s_service.test_connection(cluster_data.get('kubeconfig'))
            if test_result['status'] != 'success':
                return {
                    'success': False,
                    'error': f"集群连接失败: {test_result.get('error', 'Unknown error')}"
                }
            
            # 获取集群信息
            cluster_info = self.k8s_service.get_cluster_info(
                kubeconfig=cluster_data.get('kubeconfig')
            )
            
            # 创建集群记录
            cluster = Cluster(
                name=cluster_data['name'],
                display_name=cluster_data.get('display_name'),
                description=cluster_data.get('description'),
                api_server=cluster_info['api_server'],
                kubeconfig=cluster_data.get('kubeconfig'),
                cluster_type=cluster_data.get('cluster_type', 'kubernetes'),
                cloud_provider=cluster_data.get('cloud_provider'),
                region=cluster_data.get('region'),
                kubernetes_version=cluster_info['kubernetes_version'],
                status='healthy',
                node_count=cluster_info['node_count'],
                namespace_count=cluster_info['namespace_count'],
                pod_count=cluster_info['pod_count'],
                service_count=cluster_info['service_count'],
                cpu_capacity=cluster_info['cpu_capacity'],
                memory_capacity=cluster_info['memory_capacity'],
                last_heartbeat=datetime.utcnow()
            )
            
            db.session.add(cluster)
            db.session.flush()  # 获取 cluster.id
            
            # 同步节点信息
            self._sync_cluster_nodes(cluster)
            
            # 同步组件信息
            self._sync_cluster_components(cluster)
            
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=user_id,
                action='create',
                resource_type='cluster',
                resource_id=str(cluster.id),
                resource_name=cluster.name,
                status='success',
                new_values=cluster.to_dict()
            )
            
            return {
                'success': True,
                'cluster': cluster.to_dict(),
                'message': '集群注册成功'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to register cluster: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_cluster_status(self, cluster_id: int) -> Dict[str, Any]:
        """更新集群状态"""
        try:
            cluster = Cluster.query.get(cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            # 测试集群连接
            test_result = self.k8s_service.test_connection(cluster.kubeconfig)
            
            if test_result['status'] == 'success':
                # 获取最新集群信息
                cluster_info = self.k8s_service.get_cluster_info(
                    cluster_name=cluster.name,
                    kubeconfig=cluster.kubeconfig
                )
                
                # 更新集群信息
                cluster.status = 'healthy'
                cluster.kubernetes_version = cluster_info['kubernetes_version']
                cluster.node_count = cluster_info['node_count']
                cluster.namespace_count = cluster_info['namespace_count']
                cluster.pod_count = cluster_info['pod_count']
                cluster.service_count = cluster_info['service_count']
                cluster.cpu_capacity = cluster_info['cpu_capacity']
                cluster.memory_capacity = cluster_info['memory_capacity']
                cluster.last_heartbeat = datetime.utcnow()
                
                # 同步节点信息
                self._sync_cluster_nodes(cluster)
                
                # 同步组件信息
                self._sync_cluster_components(cluster)
                
            else:
                cluster.status = 'unhealthy'
            
            db.session.commit()
            
            return {
                'success': True,
                'cluster': cluster.to_dict(),
                'status': cluster.status
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to update cluster status: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_cluster_health(self, cluster_id: int) -> Dict[str, Any]:
        """获取集群健康状态"""
        try:
            cluster = Cluster.query.get(cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            # 获取节点健康状态
            nodes = ClusterNode.query.filter_by(cluster_id=cluster_id).all()
            node_health = {
                'total': len(nodes),
                'ready': sum(1 for node in nodes if node.status == 'ready'),
                'not_ready': sum(1 for node in nodes if node.status == 'not_ready'),
                'unknown': sum(1 for node in nodes if node.status == 'unknown')
            }
            
            # 获取组件健康状态
            components = ClusterComponent.query.filter_by(cluster_id=cluster_id).all()
            component_health = {
                'total': len(components),
                'healthy': sum(1 for comp in components if comp.status == 'healthy'),
                'unhealthy': sum(1 for comp in components if comp.status == 'unhealthy'),
                'unknown': sum(1 for comp in components if comp.status == 'unknown')
            }
            
            # 计算整体健康分数
            health_score = self._calculate_health_score(cluster, node_health, component_health)
            
            return {
                'success': True,
                'cluster_id': cluster_id,
                'cluster_name': cluster.name,
                'overall_status': cluster.status,
                'health_score': health_score,
                'last_heartbeat': cluster.last_heartbeat.isoformat() if cluster.last_heartbeat else None,
                'node_health': node_health,
                'component_health': component_health,
                'resource_usage': {
                    'cpu_usage': cluster.cpu_usage,
                    'memory_usage': cluster.memory_usage,
                    'storage_usage': cluster.storage_usage
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get cluster health: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_cluster_nodes(self, cluster_id: int) -> Dict[str, Any]:
        """获取集群节点信息"""
        try:
            cluster = Cluster.query.get(cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            nodes = ClusterNode.query.filter_by(cluster_id=cluster_id).all()
            
            return {
                'success': True,
                'cluster_id': cluster_id,
                'cluster_name': cluster.name,
                'nodes': [node.to_dict() for node in nodes]
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get cluster nodes: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def sync_all_clusters(self) -> Dict[str, Any]:
        """同步所有集群状态"""
        try:
            clusters = Cluster.query.filter_by(is_active=True).all()
            results = []
            
            for cluster in clusters:
                result = self.update_cluster_status(cluster.id)
                results.append({
                    'cluster_id': cluster.id,
                    'cluster_name': cluster.name,
                    'success': result['success'],
                    'status': result.get('status'),
                    'error': result.get('error')
                })
            
            return {
                'success': True,
                'total_clusters': len(clusters),
                'results': results
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to sync all clusters: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _sync_cluster_nodes(self, cluster: Cluster):
        """同步集群节点信息"""
        try:
            nodes_data = self.k8s_service.get_nodes(
                cluster_name=cluster.name,
                kubeconfig=cluster.kubeconfig
            )
            
            # 清除旧的节点记录
            ClusterNode.query.filter_by(cluster_id=cluster.id).delete()
            
            # 创建新的节点记录
            for node_data in nodes_data:
                node = ClusterNode(
                    cluster_id=cluster.id,
                    name=node_data['name'],
                    hostname=node_data.get('hostname'),
                    internal_ip=node_data.get('internal_ip'),
                    external_ip=node_data.get('external_ip'),
                    node_type=node_data['roles'][0] if node_data['roles'] else 'worker',
                    os_image=node_data.get('os_image'),
                    kernel_version=node_data.get('kernel_version'),
                    container_runtime=node_data.get('container_runtime'),
                    kubelet_version=node_data.get('version'),
                    status=node_data['status'].lower(),
                    conditions=node_data.get('conditions'),
                    cpu_capacity=self.k8s_service._parse_cpu(node_data.get('cpu_capacity', '0')),
                    memory_capacity=self.k8s_service._parse_memory(node_data.get('memory_capacity', '0Ki')),
                    pod_capacity=int(node_data.get('pod_capacity', '0')),
                    labels=node_data.get('labels'),
                    taints=node_data.get('taints'),
                    last_heartbeat=datetime.utcnow()
                )
                db.session.add(node)
                
        except Exception as e:
            current_app.logger.error(f"Failed to sync cluster nodes: {e}")
            raise
    
    def _sync_cluster_components(self, cluster: Cluster):
        """同步集群组件信息"""
        try:
            # 这里可以扩展获取更多组件信息
            # 目前先创建一些基础组件记录
            
            # 清除旧的组件记录
            ClusterComponent.query.filter_by(cluster_id=cluster.id).delete()
            
            # 基础系统组件
            system_components = [
                {'name': 'kube-apiserver', 'namespace': 'kube-system', 'component_type': 'system'},
                {'name': 'kube-controller-manager', 'namespace': 'kube-system', 'component_type': 'system'},
                {'name': 'kube-scheduler', 'namespace': 'kube-system', 'component_type': 'system'},
                {'name': 'etcd', 'namespace': 'kube-system', 'component_type': 'system'},
                {'name': 'kube-proxy', 'namespace': 'kube-system', 'component_type': 'system'},
                {'name': 'coredns', 'namespace': 'kube-system', 'component_type': 'addon'},
            ]
            
            for comp_data in system_components:
                component = ClusterComponent(
                    cluster_id=cluster.id,
                    name=comp_data['name'],
                    component_type=comp_data['component_type'],
                    namespace=comp_data['namespace'],
                    status='healthy',  # 默认状态，后续可以通过实际检查更新
                    last_check=datetime.utcnow()
                )
                db.session.add(component)
                
        except Exception as e:
            current_app.logger.error(f"Failed to sync cluster components: {e}")
            raise
    
    def _calculate_health_score(self, cluster: Cluster, node_health: Dict, component_health: Dict) -> float:
        """计算集群健康分数"""
        try:
            score = 100.0
            
            # 节点健康分数 (40%)
            if node_health['total'] > 0:
                node_score = (node_health['ready'] / node_health['total']) * 40
            else:
                node_score = 0
            
            # 组件健康分数 (30%)
            if component_health['total'] > 0:
                component_score = (component_health['healthy'] / component_health['total']) * 30
            else:
                component_score = 30  # 如果没有组件信息，给默认分数
            
            # 连接状态分数 (20%)
            connection_score = 20 if cluster.status == 'healthy' else 0
            
            # 最后心跳分数 (10%)
            heartbeat_score = 10
            if cluster.last_heartbeat:
                time_diff = datetime.utcnow() - cluster.last_heartbeat
                if time_diff > timedelta(minutes=10):
                    heartbeat_score = 0
                elif time_diff > timedelta(minutes=5):
                    heartbeat_score = 5
            else:
                heartbeat_score = 0
            
            total_score = node_score + component_score + connection_score + heartbeat_score
            return round(total_score, 1)
            
        except Exception as e:
            current_app.logger.error(f"Failed to calculate health score: {e}")
            return 0.0

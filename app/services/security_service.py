"""
安全服务
"""
import uuid
import subprocess
import json
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import current_app
from app import db
from app.models.security import SecurityScan, NetworkPolicy
from app.models.cluster import Cluster
from app.models.audit import AuditLog
from app.services.kubernetes_service import KubernetesService


class SecurityService:
    """安全服务类"""
    
    def __init__(self):
        self.k8s_service = KubernetesService()
        self.trivy_cache_dir = current_app.config.get('TRIVY_CACHE_DIR', '/tmp/trivy-cache')
    
    def scan_image(self, image_name: str, image_tag: str = 'latest', 
                   user_id: int = None) -> Dict[str, Any]:
        """扫描镜像安全漏洞"""
        try:
            # 生成扫描ID
            scan_id = str(uuid.uuid4())
            
            # 创建扫描记录
            security_scan = SecurityScan(
                scan_id=scan_id,
                scan_type='image',
                scanner='trivy',
                target_type='image',
                target_name=f"{image_name}:{image_tag}",
                image_name=image_name,
                image_tag=image_tag,
                status='running',
                initiated_by=user_id
            )
            
            db.session.add(security_scan)
            db.session.commit()
            
            # 执行 Trivy 扫描
            result = self._run_trivy_scan(f"{image_name}:{image_tag}")
            
            if result['success']:
                # 更新扫描结果
                scan_data = result['data']
                
                security_scan.status = 'completed'
                security_scan.total_vulnerabilities = scan_data.get('total_vulnerabilities', 0)
                security_scan.critical_count = scan_data.get('critical_count', 0)
                security_scan.high_count = scan_data.get('high_count', 0)
                security_scan.medium_count = scan_data.get('medium_count', 0)
                security_scan.low_count = scan_data.get('low_count', 0)
                security_scan.unknown_count = scan_data.get('unknown_count', 0)
                security_scan.vulnerabilities = scan_data.get('vulnerabilities', [])
                security_scan.packages = scan_data.get('packages', [])
                security_scan.risk_score = self._calculate_risk_score(scan_data)
                security_scan.risk_level = self._determine_risk_level(security_scan.risk_score)
                security_scan.completed_at = datetime.utcnow()
                security_scan.duration = int((security_scan.completed_at - security_scan.started_at).total_seconds())
                
                db.session.commit()
                
                # 记录审计日志
                if user_id:
                    AuditLog.log_action(
                        user_id=user_id,
                        action='scan',
                        resource_type='image',
                        resource_name=f"{image_name}:{image_tag}",
                        status='success',
                        metadata={
                            'scan_id': scan_id,
                            'total_vulnerabilities': security_scan.total_vulnerabilities,
                            'risk_level': security_scan.risk_level
                        }
                    )
                
                return {
                    'success': True,
                    'scan': security_scan.to_dict(),
                    'message': '镜像扫描完成'
                }
            else:
                # 更新扫描状态为失败
                security_scan.status = 'failed'
                security_scan.completed_at = datetime.utcnow()
                db.session.commit()
                
                return {
                    'success': False,
                    'error': result['error'],
                    'scan_id': scan_id
                }
                
        except Exception as e:
            if 'security_scan' in locals():
                security_scan.status = 'failed'
                db.session.commit()
            
            current_app.logger.error(f"Failed to scan image: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def scan_cluster(self, cluster_id: int, user_id: int = None) -> Dict[str, Any]:
        """扫描集群安全配置"""
        try:
            cluster = Cluster.query.get(cluster_id)
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            # 生成扫描ID
            scan_id = str(uuid.uuid4())
            
            # 创建扫描记录
            security_scan = SecurityScan(
                scan_id=scan_id,
                scan_type='cluster',
                scanner='custom',
                target_type='cluster',
                target_name=cluster.name,
                cluster_id=cluster_id,
                status='running',
                initiated_by=user_id
            )
            
            db.session.add(security_scan)
            db.session.commit()
            
            # 执行集群安全检查
            result = self._run_cluster_security_check(cluster)
            
            if result['success']:
                scan_data = result['data']
                
                security_scan.status = 'completed'
                security_scan.compliance_checks = scan_data.get('compliance_checks', [])
                security_scan.policy_violations = scan_data.get('policy_violations', [])
                security_scan.recommendations = scan_data.get('recommendations', [])
                security_scan.risk_score = self._calculate_cluster_risk_score(scan_data)
                security_scan.risk_level = self._determine_risk_level(security_scan.risk_score)
                security_scan.completed_at = datetime.utcnow()
                security_scan.duration = int((security_scan.completed_at - security_scan.started_at).total_seconds())
                
                db.session.commit()
                
                # 记录审计日志
                if user_id:
                    AuditLog.log_action(
                        user_id=user_id,
                        action='scan',
                        resource_type='cluster',
                        resource_id=str(cluster_id),
                        resource_name=cluster.name,
                        cluster_id=cluster_id,
                        status='success',
                        metadata={
                            'scan_id': scan_id,
                            'risk_level': security_scan.risk_level
                        }
                    )
                
                return {
                    'success': True,
                    'scan': security_scan.to_dict(),
                    'message': '集群安全扫描完成'
                }
            else:
                security_scan.status = 'failed'
                security_scan.completed_at = datetime.utcnow()
                db.session.commit()
                
                return {
                    'success': False,
                    'error': result['error'],
                    'scan_id': scan_id
                }
                
        except Exception as e:
            if 'security_scan' in locals():
                security_scan.status = 'failed'
                db.session.commit()
            
            current_app.logger.error(f"Failed to scan cluster: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_network_policy(self, policy_data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """创建网络策略"""
        try:
            cluster = Cluster.query.get(policy_data['cluster_id'])
            if not cluster:
                return {
                    'success': False,
                    'error': 'Cluster not found'
                }
            
            # 创建网络策略记录
            network_policy = NetworkPolicy(
                name=policy_data['name'],
                namespace=policy_data['namespace'],
                cluster_id=policy_data['cluster_id'],
                policy_types=policy_data.get('policy_types', ['Ingress']),
                pod_selector=policy_data.get('pod_selector', {}),
                ingress_rules=policy_data.get('ingress_rules', []),
                egress_rules=policy_data.get('egress_rules', []),
                labels=policy_data.get('labels', {}),
                annotations=policy_data.get('annotations', {})
            )
            
            db.session.add(network_policy)
            db.session.flush()
            
            # 应用到 Kubernetes
            result = self._apply_network_policy(network_policy, cluster)
            
            if result['success']:
                db.session.commit()
                
                # 记录审计日志
                AuditLog.log_action(
                    user_id=user_id,
                    action='create',
                    resource_type='network_policy',
                    resource_id=str(network_policy.id),
                    resource_name=network_policy.name,
                    cluster_id=cluster.id,
                    namespace=network_policy.namespace,
                    status='success',
                    new_values=network_policy.to_dict()
                )
                
                return {
                    'success': True,
                    'network_policy': network_policy.to_dict(),
                    'message': '网络策略创建成功'
                }
            else:
                db.session.rollback()
                return result
                
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to create network policy: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_security_scans(self, cluster_id: Optional[int] = None, 
                          scan_type: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
        """获取安全扫描列表"""
        try:
            query = SecurityScan.query
            
            if cluster_id:
                query = query.filter_by(cluster_id=cluster_id)
            if scan_type:
                query = query.filter_by(scan_type=scan_type)
            
            scans = query.order_by(SecurityScan.started_at.desc()).limit(limit).all()
            
            return {
                'success': True,
                'scans': [scan.to_dict() for scan in scans],
                'total': len(scans)
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get security scans: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _run_trivy_scan(self, image: str) -> Dict[str, Any]:
        """运行 Trivy 扫描"""
        try:
            # 确保缓存目录存在
            os.makedirs(self.trivy_cache_dir, exist_ok=True)
            
            # 创建临时文件保存结果
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
                temp_filename = temp_file.name
            
            try:
                # 构建 Trivy 命令
                cmd = [
                    'trivy',
                    'image',
                    '--format', 'json',
                    '--output', temp_filename,
                    '--cache-dir', self.trivy_cache_dir,
                    '--timeout', '10m',
                    image
                ]
                
                # 执行扫描
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=600  # 10分钟超时
                )
                
                if result.returncode == 0:
                    # 读取扫描结果
                    with open(temp_filename, 'r') as f:
                        scan_result = json.load(f)
                    
                    # 解析结果
                    parsed_data = self._parse_trivy_result(scan_result)
                    
                    return {
                        'success': True,
                        'data': parsed_data
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Trivy scan failed: {result.stderr}'
                    }
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_filename):
                    os.unlink(temp_filename)
                    
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Trivy scan timeout'
            }
        except FileNotFoundError:
            return {
                'success': False,
                'error': 'Trivy not found. Please install Trivy scanner.'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _parse_trivy_result(self, scan_result: Dict) -> Dict[str, Any]:
        """解析 Trivy 扫描结果"""
        vulnerabilities = []
        packages = []
        
        severity_counts = {
            'critical': 0,
            'high': 0,
            'medium': 0,
            'low': 0,
            'unknown': 0
        }
        
        # 解析结果
        results = scan_result.get('Results', [])
        for result in results:
            target = result.get('Target', '')
            vulns = result.get('Vulnerabilities', [])
            
            for vuln in vulns:
                severity = vuln.get('Severity', 'UNKNOWN').lower()
                if severity in severity_counts:
                    severity_counts[severity] += 1
                
                vulnerabilities.append({
                    'vulnerability_id': vuln.get('VulnerabilityID'),
                    'package_name': vuln.get('PkgName'),
                    'installed_version': vuln.get('InstalledVersion'),
                    'fixed_version': vuln.get('FixedVersion'),
                    'severity': severity,
                    'title': vuln.get('Title'),
                    'description': vuln.get('Description'),
                    'references': vuln.get('References', []),
                    'target': target
                })
        
        return {
            'vulnerabilities': vulnerabilities,
            'packages': packages,
            'total_vulnerabilities': len(vulnerabilities),
            'critical_count': severity_counts['critical'],
            'high_count': severity_counts['high'],
            'medium_count': severity_counts['medium'],
            'low_count': severity_counts['low'],
            'unknown_count': severity_counts['unknown']
        }
    
    def _run_cluster_security_check(self, cluster: Cluster) -> Dict[str, Any]:
        """运行集群安全检查"""
        try:
            compliance_checks = []
            policy_violations = []
            recommendations = []
            
            # 这里可以实现各种安全检查
            # 例如：检查 RBAC 配置、Pod 安全策略、网络策略等
            
            # 示例检查：检查是否启用了 RBAC
            try:
                rbac_api = self.k8s_service.get_rbac_v1_api(cluster.name, cluster.kubeconfig)
                roles = rbac_api.list_cluster_role()
                
                compliance_checks.append({
                    'check_name': 'RBAC Enabled',
                    'status': 'pass' if len(roles.items) > 0 else 'fail',
                    'description': 'Check if RBAC is enabled in the cluster',
                    'details': f'Found {len(roles.items)} cluster roles'
                })
            except Exception as e:
                compliance_checks.append({
                    'check_name': 'RBAC Enabled',
                    'status': 'error',
                    'description': 'Check if RBAC is enabled in the cluster',
                    'details': f'Error checking RBAC: {str(e)}'
                })
            
            # 示例检查：检查网络策略
            try:
                networking_api = self.k8s_service.get_networking_v1_api(cluster.name, cluster.kubeconfig)
                network_policies = networking_api.list_network_policy_for_all_namespaces()
                
                compliance_checks.append({
                    'check_name': 'Network Policies',
                    'status': 'pass' if len(network_policies.items) > 0 else 'warning',
                    'description': 'Check if network policies are configured',
                    'details': f'Found {len(network_policies.items)} network policies'
                })
                
                if len(network_policies.items) == 0:
                    recommendations.append({
                        'category': 'Network Security',
                        'recommendation': 'Configure network policies to restrict pod-to-pod communication',
                        'priority': 'medium'
                    })
            except Exception as e:
                compliance_checks.append({
                    'check_name': 'Network Policies',
                    'status': 'error',
                    'description': 'Check if network policies are configured',
                    'details': f'Error checking network policies: {str(e)}'
                })
            
            return {
                'success': True,
                'data': {
                    'compliance_checks': compliance_checks,
                    'policy_violations': policy_violations,
                    'recommendations': recommendations
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _apply_network_policy(self, network_policy: NetworkPolicy, cluster: Cluster) -> Dict[str, Any]:
        """应用网络策略到 Kubernetes"""
        try:
            networking_api = self.k8s_service.get_networking_v1_api(cluster.name, cluster.kubeconfig)
            
            # 构建网络策略清单
            policy_manifest = {
                'apiVersion': 'networking.k8s.io/v1',
                'kind': 'NetworkPolicy',
                'metadata': {
                    'name': network_policy.name,
                    'namespace': network_policy.namespace,
                    'labels': network_policy.labels or {},
                    'annotations': network_policy.annotations or {}
                },
                'spec': {
                    'podSelector': network_policy.pod_selector or {},
                    'policyTypes': network_policy.policy_types or ['Ingress']
                }
            }
            
            if network_policy.ingress_rules:
                policy_manifest['spec']['ingress'] = network_policy.ingress_rules
            
            if network_policy.egress_rules:
                policy_manifest['spec']['egress'] = network_policy.egress_rules
            
            # 创建网络策略
            networking_api.create_namespaced_network_policy(
                namespace=network_policy.namespace,
                body=policy_manifest
            )
            
            return {
                'success': True,
                'message': 'Network policy applied successfully'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to apply network policy: {str(e)}'
            }
    
    def _calculate_risk_score(self, scan_data: Dict) -> float:
        """计算风险分数"""
        critical = scan_data.get('critical_count', 0)
        high = scan_data.get('high_count', 0)
        medium = scan_data.get('medium_count', 0)
        low = scan_data.get('low_count', 0)
        
        # 权重计算
        score = (critical * 10) + (high * 7) + (medium * 4) + (low * 1)
        
        # 归一化到 0-10 范围
        max_score = 100  # 假设最大分数
        normalized_score = min(score / max_score * 10, 10)
        
        return round(normalized_score, 1)
    
    def _calculate_cluster_risk_score(self, scan_data: Dict) -> float:
        """计算集群风险分数"""
        compliance_checks = scan_data.get('compliance_checks', [])
        policy_violations = scan_data.get('policy_violations', [])
        
        total_checks = len(compliance_checks)
        failed_checks = sum(1 for check in compliance_checks if check.get('status') == 'fail')
        violations = len(policy_violations)
        
        if total_checks == 0:
            return 5.0  # 默认中等风险
        
        # 计算失败率
        failure_rate = failed_checks / total_checks
        violation_penalty = min(violations * 0.5, 5)  # 每个违规增加0.5分，最多5分
        
        score = (failure_rate * 5) + violation_penalty
        return round(min(score, 10), 1)
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险等级"""
        if risk_score >= 8:
            return 'critical'
        elif risk_score >= 6:
            return 'high'
        elif risk_score >= 4:
            return 'medium'
        else:
            return 'low'

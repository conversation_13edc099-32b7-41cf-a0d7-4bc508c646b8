"""
Kubernetes 客户端服务
"""
import os
import yaml
import base64
from typing import Dict, List, Optional, Any
from kubernetes import client, config
from kubernetes.client.rest import ApiException
from flask import current_app


class KubernetesService:
    """Kubernetes 客户端服务类"""
    
    def __init__(self):
        self._clients = {}  # 缓存不同集群的客户端
    
    def get_client(self, cluster_name: str = None, kubeconfig: str = None) -> client.ApiClient:
        """获取 Kubernetes 客户端"""
        if cluster_name and cluster_name in self._clients:
            return self._clients[cluster_name]
        
        try:
            if kubeconfig:
                # 使用提供的 kubeconfig
                config_dict = yaml.safe_load(base64.b64decode(kubeconfig))
                api_client = config.new_client_from_config_dict(config_dict)
            else:
                # 使用默认配置
                try:
                    # 尝试集群内配置
                    config.load_incluster_config()
                except config.ConfigException:
                    # 使用本地配置
                    config.load_kube_config()
                api_client = client.ApiClient()
            
            if cluster_name:
                self._clients[cluster_name] = api_client
            
            return api_client
            
        except Exception as e:
            current_app.logger.error(f"Failed to create Kubernetes client: {e}")
            raise
    
    def get_core_v1_api(self, cluster_name: str = None, kubeconfig: str = None) -> client.CoreV1Api:
        """获取 Core V1 API 客户端"""
        api_client = self.get_client(cluster_name, kubeconfig)
        return client.CoreV1Api(api_client)
    
    def get_apps_v1_api(self, cluster_name: str = None, kubeconfig: str = None) -> client.AppsV1Api:
        """获取 Apps V1 API 客户端"""
        api_client = self.get_client(cluster_name, kubeconfig)
        return client.AppsV1Api(api_client)
    
    def get_networking_v1_api(self, cluster_name: str = None, kubeconfig: str = None) -> client.NetworkingV1Api:
        """获取 Networking V1 API 客户端"""
        api_client = self.get_client(cluster_name, kubeconfig)
        return client.NetworkingV1Api(api_client)
    
    def get_rbac_v1_api(self, cluster_name: str = None, kubeconfig: str = None) -> client.RbacAuthorizationV1Api:
        """获取 RBAC V1 API 客户端"""
        api_client = self.get_client(cluster_name, kubeconfig)
        return client.RbacAuthorizationV1Api(api_client)
    
    def get_metrics_api(self, cluster_name: str = None, kubeconfig: str = None) -> client.CustomObjectsApi:
        """获取 Metrics API 客户端"""
        api_client = self.get_client(cluster_name, kubeconfig)
        return client.CustomObjectsApi(api_client)
    
    def test_connection(self, kubeconfig: str = None) -> Dict[str, Any]:
        """测试集群连接"""
        try:
            core_v1 = self.get_core_v1_api(kubeconfig=kubeconfig)
            
            # 获取集群版本信息
            version_api = client.VersionApi(self.get_client(kubeconfig=kubeconfig))
            version_info = version_api.get_code()
            
            # 获取节点信息
            nodes = core_v1.list_node()
            
            # 获取命名空间信息
            namespaces = core_v1.list_namespace()
            
            return {
                'status': 'success',
                'version': {
                    'kubernetes': version_info.git_version,
                    'platform': version_info.platform
                },
                'nodes': len(nodes.items),
                'namespaces': len(namespaces.items),
                'api_server': core_v1.api_client.configuration.host
            }
            
        except ApiException as e:
            return {
                'status': 'error',
                'error': f'API Error: {e.status} - {e.reason}',
                'details': e.body
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def get_cluster_info(self, cluster_name: str = None, kubeconfig: str = None) -> Dict[str, Any]:
        """获取集群基本信息"""
        try:
            core_v1 = self.get_core_v1_api(cluster_name, kubeconfig)
            version_api = client.VersionApi(self.get_client(cluster_name, kubeconfig))
            
            # 版本信息
            version_info = version_api.get_code()
            
            # 节点信息
            nodes = core_v1.list_node()
            node_count = len(nodes.items)
            
            # 计算资源容量
            total_cpu = 0
            total_memory = 0
            ready_nodes = 0
            
            for node in nodes.items:
                if node.status.allocatable:
                    cpu = node.status.allocatable.get('cpu', '0')
                    memory = node.status.allocatable.get('memory', '0Ki')
                    
                    total_cpu += self._parse_cpu(cpu)
                    total_memory += self._parse_memory(memory)
                
                # 检查节点状态
                for condition in node.status.conditions:
                    if condition.type == 'Ready' and condition.status == 'True':
                        ready_nodes += 1
                        break
            
            # 命名空间信息
            namespaces = core_v1.list_namespace()
            namespace_count = len(namespaces.items)
            
            # Pod 信息
            pods = core_v1.list_pod_for_all_namespaces()
            pod_count = len(pods.items)
            
            # 服务信息
            services = core_v1.list_service_for_all_namespaces()
            service_count = len(services.items)
            
            return {
                'kubernetes_version': version_info.git_version,
                'platform': version_info.platform,
                'api_server': core_v1.api_client.configuration.host,
                'node_count': node_count,
                'ready_nodes': ready_nodes,
                'namespace_count': namespace_count,
                'pod_count': pod_count,
                'service_count': service_count,
                'cpu_capacity': total_cpu,
                'memory_capacity': total_memory
            }
            
        except Exception as e:
            current_app.logger.error(f"Failed to get cluster info: {e}")
            raise
    
    def get_nodes(self, cluster_name: str = None, kubeconfig: str = None) -> List[Dict[str, Any]]:
        """获取节点列表"""
        try:
            core_v1 = self.get_core_v1_api(cluster_name, kubeconfig)
            nodes = core_v1.list_node()
            
            node_list = []
            for node in nodes.items:
                node_info = {
                    'name': node.metadata.name,
                    'status': 'Unknown',
                    'roles': [],
                    'age': node.metadata.creation_timestamp,
                    'version': node.status.node_info.kubelet_version,
                    'internal_ip': None,
                    'external_ip': None,
                    'os_image': node.status.node_info.os_image,
                    'kernel_version': node.status.node_info.kernel_version,
                    'container_runtime': node.status.node_info.container_runtime_version,
                    'cpu_capacity': node.status.capacity.get('cpu', '0'),
                    'memory_capacity': node.status.capacity.get('memory', '0Ki'),
                    'pod_capacity': node.status.capacity.get('pods', '0'),
                    'labels': node.metadata.labels or {},
                    'taints': [
                        {
                            'key': taint.key,
                            'value': taint.value,
                            'effect': taint.effect
                        } for taint in (node.spec.taints or [])
                    ],
                    'conditions': []
                }
                
                # 解析节点状态
                for condition in node.status.conditions:
                    node_info['conditions'].append({
                        'type': condition.type,
                        'status': condition.status,
                        'reason': condition.reason,
                        'message': condition.message,
                        'last_transition_time': condition.last_transition_time
                    })
                    
                    if condition.type == 'Ready':
                        node_info['status'] = 'Ready' if condition.status == 'True' else 'NotReady'
                
                # 解析节点角色
                labels = node.metadata.labels or {}
                if 'node-role.kubernetes.io/master' in labels or 'node-role.kubernetes.io/control-plane' in labels:
                    node_info['roles'].append('master')
                if 'node-role.kubernetes.io/worker' in labels:
                    node_info['roles'].append('worker')
                if not node_info['roles']:
                    node_info['roles'].append('worker')  # 默认为 worker
                
                # 解析 IP 地址
                for address in node.status.addresses:
                    if address.type == 'InternalIP':
                        node_info['internal_ip'] = address.address
                    elif address.type == 'ExternalIP':
                        node_info['external_ip'] = address.address
                
                node_list.append(node_info)
            
            return node_list
            
        except Exception as e:
            current_app.logger.error(f"Failed to get nodes: {e}")
            raise
    
    def get_namespaces(self, cluster_name: str = None, kubeconfig: str = None) -> List[Dict[str, Any]]:
        """获取命名空间列表"""
        try:
            core_v1 = self.get_core_v1_api(cluster_name, kubeconfig)
            namespaces = core_v1.list_namespace()
            
            namespace_list = []
            for ns in namespaces.items:
                namespace_info = {
                    'name': ns.metadata.name,
                    'status': ns.status.phase,
                    'age': ns.metadata.creation_timestamp,
                    'labels': ns.metadata.labels or {},
                    'annotations': ns.metadata.annotations or {}
                }
                namespace_list.append(namespace_info)
            
            return namespace_list
            
        except Exception as e:
            current_app.logger.error(f"Failed to get namespaces: {e}")
            raise
    
    def _parse_cpu(self, cpu_str: str) -> float:
        """解析 CPU 资源"""
        if cpu_str.endswith('m'):
            return float(cpu_str[:-1]) / 1000
        return float(cpu_str)
    
    def _parse_memory(self, memory_str: str) -> int:
        """解析内存资源（返回字节数）"""
        units = {
            'Ki': 1024,
            'Mi': 1024 ** 2,
            'Gi': 1024 ** 3,
            'Ti': 1024 ** 4,
            'K': 1000,
            'M': 1000 ** 2,
            'G': 1000 ** 3,
            'T': 1000 ** 4
        }
        
        for unit, multiplier in units.items():
            if memory_str.endswith(unit):
                return int(float(memory_str[:-len(unit)]) * multiplier)
        
        return int(memory_str)
    
    def clear_client_cache(self, cluster_name: str = None):
        """清除客户端缓存"""
        if cluster_name:
            self._clients.pop(cluster_name, None)
        else:
            self._clients.clear()

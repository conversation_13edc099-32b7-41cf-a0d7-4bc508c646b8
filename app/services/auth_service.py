"""
认证授权服务
"""
import ldap
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import current_app
from app import db
from app.models.user import User, Role, Permission
from app.models.audit import AuditLog


class AuthService:
    """认证授权服务类"""
    
    def __init__(self):
        self.ldap_host = current_app.config.get('LDAP_HOST')
        self.ldap_port = current_app.config.get('LDAP_PORT', 389)
        self.ldap_use_ssl = current_app.config.get('LDAP_USE_SSL', False)
        self.ldap_base_dn = current_app.config.get('LDAP_BASE_DN')
        self.ldap_user_dn = current_app.config.get('LDAP_USER_DN')
    
    def create_user(self, user_data: Dict[str, Any], creator_id: int) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 检查用户名是否已存在
            if User.query.filter_by(username=user_data['username']).first():
                return {
                    'success': False,
                    'error': 'Username already exists'
                }
            
            # 检查邮箱是否已存在
            if User.query.filter_by(email=user_data['email']).first():
                return {
                    'success': False,
                    'error': 'Email already exists'
                }
            
            # 创建用户
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                full_name=user_data.get('full_name'),
                department=user_data.get('department'),
                phone=user_data.get('phone'),
                auth_type=user_data.get('auth_type', 'local'),
                is_active=user_data.get('is_active', True)
            )
            
            # 设置密码（仅本地认证）
            if user.auth_type == 'local' and user_data.get('password'):
                user.set_password(user_data['password'])
            
            db.session.add(user)
            db.session.flush()  # 获取 user.id
            
            # 分配角色
            role_ids = user_data.get('role_ids', [])
            for role_id in role_ids:
                role = Role.query.get(role_id)
                if role:
                    user.roles.append(role)
            
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=creator_id,
                action='create',
                resource_type='user',
                resource_id=str(user.id),
                resource_name=user.username,
                status='success',
                new_values=user.to_dict()
            )
            
            return {
                'success': True,
                'user': user.to_dict(),
                'message': '用户创建成功'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to create user: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_user(self, user_id: int, user_data: Dict[str, Any], updater_id: int) -> Dict[str, Any]:
        """更新用户"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {
                    'success': False,
                    'error': 'User not found'
                }
            
            # 保存旧值用于审计
            old_values = user.to_dict()
            
            # 更新用户信息
            updatable_fields = [
                'email', 'full_name', 'department', 'phone', 'is_active'
            ]
            
            for field in updatable_fields:
                if field in user_data:
                    setattr(user, field, user_data[field])
            
            # 更新密码（仅本地认证）
            if user.auth_type == 'local' and user_data.get('password'):
                user.set_password(user_data['password'])
            
            # 更新角色
            if 'role_ids' in user_data:
                user.roles.clear()
                for role_id in user_data['role_ids']:
                    role = Role.query.get(role_id)
                    if role:
                        user.roles.append(role)
            
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=updater_id,
                action='update',
                resource_type='user',
                resource_id=str(user.id),
                resource_name=user.username,
                status='success',
                old_values=old_values,
                new_values=user.to_dict()
            )
            
            return {
                'success': True,
                'user': user.to_dict(),
                'message': '用户更新成功'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to update user: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_user(self, user_id: int, deleter_id: int) -> Dict[str, Any]:
        """删除用户"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {
                    'success': False,
                    'error': 'User not found'
                }
            
            # 不能删除自己
            if user_id == deleter_id:
                return {
                    'success': False,
                    'error': 'Cannot delete yourself'
                }
            
            user_data = user.to_dict()
            
            db.session.delete(user)
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=deleter_id,
                action='delete',
                resource_type='user',
                resource_id=str(user_id),
                resource_name=user_data['username'],
                status='success',
                old_values=user_data
            )
            
            return {
                'success': True,
                'message': '用户删除成功'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to delete user: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def authenticate_ldap(self, username: str, password: str) -> Dict[str, Any]:
        """LDAP 认证"""
        try:
            if not self.ldap_host:
                return {
                    'success': False,
                    'error': 'LDAP not configured'
                }
            
            # 构建 LDAP 连接
            ldap_url = f"{'ldaps' if self.ldap_use_ssl else 'ldap'}://{self.ldap_host}:{self.ldap_port}"
            conn = ldap.initialize(ldap_url)
            
            # 设置 LDAP 选项
            conn.set_option(ldap.OPT_REFERRALS, 0)
            conn.set_option(ldap.OPT_PROTOCOL_VERSION, 3)
            
            # 构建用户 DN
            user_dn = f"uid={username},{self.ldap_user_dn}"
            
            # 尝试绑定
            conn.simple_bind_s(user_dn, password)
            
            # 搜索用户信息
            search_filter = f"(uid={username})"
            attributes = ['cn', 'mail', 'departmentNumber', 'telephoneNumber']
            
            result = conn.search_s(
                self.ldap_user_dn,
                ldap.SCOPE_SUBTREE,
                search_filter,
                attributes
            )
            
            if result:
                dn, attrs = result[0]
                user_info = {
                    'username': username,
                    'full_name': attrs.get('cn', [b''])[0].decode('utf-8'),
                    'email': attrs.get('mail', [b''])[0].decode('utf-8'),
                    'department': attrs.get('departmentNumber', [b''])[0].decode('utf-8'),
                    'phone': attrs.get('telephoneNumber', [b''])[0].decode('utf-8'),
                    'auth_type': 'ldap'
                }
                
                # 查找或创建本地用户记录
                user = User.query.filter_by(username=username).first()
                if not user:
                    user = User(
                        username=user_info['username'],
                        email=user_info['email'],
                        full_name=user_info['full_name'],
                        department=user_info['department'],
                        phone=user_info['phone'],
                        auth_type='ldap'
                    )
                    db.session.add(user)
                else:
                    # 更新用户信息
                    user.email = user_info['email']
                    user.full_name = user_info['full_name']
                    user.department = user_info['department']
                    user.phone = user_info['phone']
                
                user.last_login = datetime.utcnow()
                db.session.commit()
                
                return {
                    'success': True,
                    'user': user.to_dict()
                }
            else:
                return {
                    'success': False,
                    'error': 'User not found in LDAP'
                }
                
        except ldap.INVALID_CREDENTIALS:
            return {
                'success': False,
                'error': 'Invalid credentials'
            }
        except ldap.LDAPError as e:
            return {
                'success': False,
                'error': f'LDAP error: {str(e)}'
            }
        except Exception as e:
            current_app.logger.error(f"LDAP authentication failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            if 'conn' in locals():
                conn.unbind()
    
    def create_role(self, role_data: Dict[str, Any], creator_id: int) -> Dict[str, Any]:
        """创建角色"""
        try:
            # 检查角色名是否已存在
            if Role.query.filter_by(name=role_data['name']).first():
                return {
                    'success': False,
                    'error': 'Role name already exists'
                }
            
            # 创建角色
            role = Role(
                name=role_data['name'],
                description=role_data.get('description'),
                is_system=False  # 用户创建的角色不是系统角色
            )
            
            db.session.add(role)
            db.session.flush()
            
            # 分配权限
            permission_ids = role_data.get('permission_ids', [])
            for permission_id in permission_ids:
                permission = Permission.query.get(permission_id)
                if permission:
                    role.permissions.append(permission)
            
            db.session.commit()
            
            # 记录审计日志
            AuditLog.log_action(
                user_id=creator_id,
                action='create',
                resource_type='role',
                resource_id=str(role.id),
                resource_name=role.name,
                status='success',
                new_values=role.to_dict()
            )
            
            return {
                'success': True,
                'role': role.to_dict(),
                'message': '角色创建成功'
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to create role: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def check_permission(self, user_id: int, permission_name: str, 
                        resource_id: str = None, namespace: str = None) -> bool:
        """检查用户权限"""
        try:
            user = User.query.get(user_id)
            if not user or not user.is_active:
                return False
            
            # 管理员拥有所有权限
            if user.is_admin:
                return True
            
            # 检查用户角色权限
            return user.has_permission(permission_name)
            
        except Exception as e:
            current_app.logger.error(f"Failed to check permission: {e}")
            return False

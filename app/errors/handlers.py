"""
错误处理器
"""
from flask import jsonify, request
from app.errors import bp


@bp.app_errorhandler(400)
def bad_request(error):
    """400 错误处理"""
    return jsonify({
        'error': 'Bad Request',
        'message': 'The request could not be understood by the server.',
        'status_code': 400
    }), 400


@bp.app_errorhandler(401)
def unauthorized(error):
    """401 错误处理"""
    return jsonify({
        'error': 'Unauthorized',
        'message': 'Authentication is required to access this resource.',
        'status_code': 401
    }), 401


@bp.app_errorhandler(403)
def forbidden(error):
    """403 错误处理"""
    return jsonify({
        'error': 'Forbidden',
        'message': 'You do not have permission to access this resource.',
        'status_code': 403
    }), 403


@bp.app_errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({
        'error': 'Not Found',
        'message': 'The requested resource was not found.',
        'status_code': 404
    }), 404


@bp.app_errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    return jsonify({
        'error': 'Internal Server Error',
        'message': 'An internal server error occurred.',
        'status_code': 500
    }), 500


@bp.app_errorhandler(Exception)
def handle_exception(error):
    """通用异常处理"""
    if request.path.startswith('/api/'):
        return jsonify({
            'error': 'Internal Server Error',
            'message': str(error),
            'status_code': 500
        }), 500
    
    # 非 API 请求返回 HTML 错误页面
    return f"<h1>Error: {error}</h1>", 500

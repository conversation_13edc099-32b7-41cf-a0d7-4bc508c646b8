"""
安全管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp


@bp.route('/security/scans', methods=['GET'])
@jwt_required()
def get_security_scans():
    """获取安全扫描列表"""
    return jsonify({
        'message': 'Security scans API endpoint',
        'status': 'under_development'
    })


@bp.route('/security/policies', methods=['GET'])
@jwt_required()
def get_security_policies():
    """获取安全策略列表"""
    return jsonify({
        'message': 'Security policies API endpoint',
        'status': 'under_development'
    })

"""
安全管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp
from app.models.security import SecurityScan, NetworkPolicy
from app.services.security_service import SecurityService


@bp.route('/security/scans', methods=['GET'])
@jwt_required()
def get_security_scans():
    """获取安全扫描列表"""
    cluster_id = request.args.get('cluster_id', type=int)
    scan_type = request.args.get('scan_type')
    limit = request.args.get('limit', 100, type=int)

    security_service = SecurityService()
    result = security_service.get_security_scans(cluster_id, scan_type, limit)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/security/scans/image', methods=['POST'])
@jwt_required()
def scan_image():
    """扫描镜像"""
    data = request.get_json()
    user_id = get_jwt_identity()

    image_name = data.get('image_name')
    image_tag = data.get('image_tag', 'latest')

    if not image_name:
        return jsonify({
            'error': 'Missing image_name parameter'
        }), 400

    security_service = SecurityService()
    result = security_service.scan_image(image_name, image_tag, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'scan': result['scan']
        }), 201
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/security/scans/cluster', methods=['POST'])
@jwt_required()
def scan_cluster():
    """扫描集群"""
    data = request.get_json()
    user_id = get_jwt_identity()

    cluster_id = data.get('cluster_id')
    if not cluster_id:
        return jsonify({
            'error': 'Missing cluster_id parameter'
        }), 400

    security_service = SecurityService()
    result = security_service.scan_cluster(cluster_id, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'scan': result['scan']
        }), 201
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/security/scans/<scan_id>', methods=['GET'])
@jwt_required()
def get_security_scan(scan_id):
    """获取安全扫描详情"""
    scan = SecurityScan.query.filter_by(scan_id=scan_id).first()

    if not scan:
        return jsonify({
            'error': 'Security scan not found'
        }), 404

    return jsonify({
        'scan': scan.to_dict()
    })


@bp.route('/security/network-policies', methods=['GET'])
@jwt_required()
def get_network_policies():
    """获取网络策略列表"""
    cluster_id = request.args.get('cluster_id', type=int)
    namespace = request.args.get('namespace')

    query = NetworkPolicy.query

    if cluster_id:
        query = query.filter_by(cluster_id=cluster_id)
    if namespace:
        query = query.filter_by(namespace=namespace)

    policies = query.all()

    return jsonify({
        'network_policies': [policy.to_dict() for policy in policies],
        'total': len(policies)
    })


@bp.route('/security/network-policies', methods=['POST'])
@jwt_required()
def create_network_policy():
    """创建网络策略"""
    data = request.get_json()
    user_id = get_jwt_identity()

    # 验证必填字段
    required_fields = ['name', 'namespace', 'cluster_id']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'error': f'Missing required field: {field}'
            }), 400

    security_service = SecurityService()
    result = security_service.create_network_policy(data, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'network_policy': result['network_policy']
        }), 201
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/security/network-policies/<int:policy_id>', methods=['GET'])
@jwt_required()
def get_network_policy(policy_id):
    """获取网络策略详情"""
    policy = NetworkPolicy.query.get_or_404(policy_id)

    return jsonify({
        'network_policy': policy.to_dict()
    })


@bp.route('/security/network-policies/<int:policy_id>', methods=['DELETE'])
@jwt_required()
def delete_network_policy(policy_id):
    """删除网络策略"""
    policy = NetworkPolicy.query.get_or_404(policy_id)
    user_id = get_jwt_identity()

    try:
        # 这里可以添加从 Kubernetes 删除网络策略的逻辑

        db.session.delete(policy)
        db.session.commit()

        # 记录审计日志
        from app.models.audit import AuditLog
        AuditLog.log_action(
            user_id=user_id,
            action='delete',
            resource_type='network_policy',
            resource_id=str(policy_id),
            resource_name=policy.name,
            cluster_id=policy.cluster_id,
            namespace=policy.namespace,
            status='success'
        )

        return jsonify({
            'message': 'Network policy deleted successfully'
        })

    except Exception as e:
        from app import db
        db.session.rollback()
        return jsonify({
            'error': f'Failed to delete network policy: {str(e)}'
        }), 500

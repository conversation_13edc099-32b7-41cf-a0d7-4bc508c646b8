"""
用户管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp
from app.models.user import User, Role, Permission
from app.services.auth_service import AuthService
from app import db


@bp.route('/users', methods=['GET'])
@jwt_required()
def get_users():
    """获取用户列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search')

    query = User.query

    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.email.contains(search),
                User.full_name.contains(search)
            )
        )

    users = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'users': [user.to_dict() for user in users.items],
        'total': users.total,
        'pages': users.pages,
        'current_page': page
    })


@bp.route('/users/<int:user_id>', methods=['GET'])
@jwt_required()
def get_user(user_id):
    """获取用户详情"""
    user = User.query.get_or_404(user_id)

    return jsonify({
        'user': user.to_dict()
    })


@bp.route('/users', methods=['POST'])
@jwt_required()
def create_user():
    """创建用户"""
    data = request.get_json()
    creator_id = get_jwt_identity()

    # 验证必填字段
    required_fields = ['username', 'email']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'error': f'Missing required field: {field}'
            }), 400

    auth_service = AuthService()
    result = auth_service.create_user(data, creator_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'user': result['user']
        }), 201
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    """更新用户"""
    data = request.get_json()
    updater_id = get_jwt_identity()

    auth_service = AuthService()
    result = auth_service.update_user(user_id, data, updater_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'user': result['user']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    """删除用户"""
    deleter_id = get_jwt_identity()

    auth_service = AuthService()
    result = auth_service.delete_user(user_id, deleter_id)

    if result['success']:
        return jsonify({
            'message': result['message']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/roles', methods=['GET'])
@jwt_required()
def get_roles():
    """获取角色列表"""
    roles = Role.query.all()

    return jsonify({
        'roles': [role.to_dict() for role in roles],
        'total': len(roles)
    })


@bp.route('/roles', methods=['POST'])
@jwt_required()
def create_role():
    """创建角色"""
    data = request.get_json()
    creator_id = get_jwt_identity()

    if not data.get('name'):
        return jsonify({
            'error': 'Missing required field: name'
        }), 400

    auth_service = AuthService()
    result = auth_service.create_role(data, creator_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'role': result['role']
        }), 201
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/roles/<int:role_id>', methods=['GET'])
@jwt_required()
def get_role(role_id):
    """获取角色详情"""
    role = Role.query.get_or_404(role_id)

    return jsonify({
        'role': role.to_dict()
    })


@bp.route('/permissions', methods=['GET'])
@jwt_required()
def get_permissions():
    """获取权限列表"""
    permissions = Permission.query.all()

    return jsonify({
        'permissions': [perm.to_dict() for perm in permissions],
        'total': len(permissions)
    })

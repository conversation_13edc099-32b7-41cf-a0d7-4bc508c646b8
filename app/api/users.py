"""
用户管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp


@bp.route('/users', methods=['GET'])
@jwt_required()
def get_users():
    """获取用户列表"""
    return jsonify({
        'message': 'Users API endpoint',
        'status': 'under_development'
    })


@bp.route('/users', methods=['POST'])
@jwt_required()
def create_user():
    """创建用户"""
    return jsonify({
        'message': 'Create user API endpoint',
        'status': 'under_development'
    })

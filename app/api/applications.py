"""
应用管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp
from app.models.application import Application, Deployment
from app.models.audit import AuditLog
from app.services.application_service import ApplicationService
from app import db


@bp.route('/applications', methods=['GET'])
@jwt_required()
def get_applications():
    """获取应用列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    cluster_id = request.args.get('cluster_id', type=int)
    namespace = request.args.get('namespace')

    query = Application.query

    if cluster_id:
        query = query.filter_by(cluster_id=cluster_id)
    if namespace:
        query = query.filter_by(namespace=namespace)

    applications = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'applications': [app.to_dict() for app in applications.items],
        'total': applications.total,
        'pages': applications.pages,
        'current_page': page
    })


@bp.route('/applications/<int:app_id>', methods=['GET'])
@jwt_required()
def get_application(app_id):
    """获取应用详情"""
    application = Application.query.get_or_404(app_id)

    # 获取部署历史
    deployments = Deployment.query.filter_by(
        application_id=app_id
    ).order_by(Deployment.revision.desc()).limit(10).all()

    return jsonify({
        'application': application.to_dict(),
        'deployments': [dep.to_dict() for dep in deployments]
    })


@bp.route('/applications', methods=['POST'])
@jwt_required()
def deploy_application():
    """部署应用"""
    data = request.get_json()
    user_id = get_jwt_identity()

    # 验证必填字段
    required_fields = ['name', 'namespace', 'cluster_id', 'image']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'error': f'Missing required field: {field}'
            }), 400

    # 检查应用名称是否已存在
    existing_app = Application.query.filter_by(
        name=data['name'],
        namespace=data['namespace'],
        cluster_id=data['cluster_id']
    ).first()

    if existing_app:
        return jsonify({
            'error': 'Application already exists in this namespace'
        }), 400

    # 使用应用服务部署应用
    app_service = ApplicationService()
    result = app_service.deploy_application(data, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'application': result['application']
        }), 201
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>', methods=['PUT'])
@jwt_required()
def update_application(app_id):
    """更新应用"""
    data = request.get_json()
    user_id = get_jwt_identity()

    app_service = ApplicationService()
    result = app_service.update_application(app_id, data, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'application': result['application']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>/scale', methods=['POST'])
@jwt_required()
def scale_application(app_id):
    """扩缩容应用"""
    data = request.get_json()
    user_id = get_jwt_identity()

    replicas = data.get('replicas')
    if replicas is None or not isinstance(replicas, int) or replicas < 0:
        return jsonify({
            'error': 'Invalid replicas value'
        }), 400

    app_service = ApplicationService()
    result = app_service.scale_application(app_id, replicas, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'application': result['application']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>', methods=['DELETE'])
@jwt_required()
def delete_application(app_id):
    """删除应用"""
    user_id = get_jwt_identity()

    app_service = ApplicationService()
    result = app_service.delete_application(app_id, user_id)

    if result['success']:
        return jsonify({
            'message': result['message']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>', methods=['PUT'])
@jwt_required()
def update_application(app_id):
    """更新应用"""
    data = request.get_json()
    user_id = get_jwt_identity()

    app_service = ApplicationService()
    result = app_service.update_application(app_id, data, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'application': result['application']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>/scale', methods=['POST'])
@jwt_required()
def scale_application(app_id):
    """扩缩容应用"""
    data = request.get_json()
    user_id = get_jwt_identity()

    replicas = data.get('replicas')
    if replicas is None or not isinstance(replicas, int) or replicas < 0:
        return jsonify({
            'error': 'Invalid replicas value'
        }), 400

    app_service = ApplicationService()
    result = app_service.scale_application(app_id, replicas, user_id)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'application': result['application']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>', methods=['DELETE'])
@jwt_required()
def delete_application(app_id):
    """删除应用"""
    user_id = get_jwt_identity()

    app_service = ApplicationService()
    result = app_service.delete_application(app_id, user_id)

    if result['success']:
        return jsonify({
            'message': result['message']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/applications/<int:app_id>/deployments', methods=['GET'])
@jwt_required()
def get_application_deployments(app_id):
    """获取应用部署历史"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    deployments = Deployment.query.filter_by(
        application_id=app_id
    ).order_by(Deployment.revision.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return jsonify({
        'deployments': [dep.to_dict() for dep in deployments.items],
        'total': deployments.total,
        'pages': deployments.pages,
        'current_page': page
    })


@bp.route('/applications/<int:app_id>/rollback', methods=['POST'])
@jwt_required()
def rollback_application(app_id):
    """回滚应用"""
    data = request.get_json()
    user_id = get_jwt_identity()

    revision = data.get('revision')
    if not revision:
        return jsonify({
            'error': 'Missing revision parameter'
        }), 400

    # 获取目标部署记录
    target_deployment = Deployment.query.filter_by(
        application_id=app_id,
        revision=revision
    ).first()

    if not target_deployment:
        return jsonify({
            'error': 'Deployment revision not found'
        }), 404

    # 使用目标部署的配置更新应用
    app_service = ApplicationService()
    result = app_service.update_application(
        app_id,
        target_deployment.deployment_config,
        user_id
    )

    if result['success']:
        return jsonify({
            'message': f'Application rolled back to revision {revision}',
            'application': result['application']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400

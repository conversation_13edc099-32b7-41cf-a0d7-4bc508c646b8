"""
应用管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp


@bp.route('/applications', methods=['GET'])
@jwt_required()
def get_applications():
    """获取应用列表"""
    return jsonify({
        'message': 'Applications API endpoint',
        'status': 'under_development'
    })


@bp.route('/applications', methods=['POST'])
@jwt_required()
def create_application():
    """创建应用"""
    return jsonify({
        'message': 'Create application API endpoint',
        'status': 'under_development'
    })

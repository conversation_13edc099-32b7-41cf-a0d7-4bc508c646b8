"""
资源管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp


@bp.route('/resources/namespaces', methods=['GET'])
@jwt_required()
def get_namespaces():
    """获取命名空间列表"""
    return jsonify({
        'message': 'Resource namespaces API endpoint',
        'status': 'under_development'
    })


@bp.route('/resources/quotas', methods=['GET'])
@jwt_required()
def get_resource_quotas():
    """获取资源配额列表"""
    return jsonify({
        'message': 'Resource quotas API endpoint',
        'status': 'under_development'
    })

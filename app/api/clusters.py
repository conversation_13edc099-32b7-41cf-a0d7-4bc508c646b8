"""
集群管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp
from app.models.cluster import Cluster
from app.models.audit import AuditLog
from app.services.cluster_service import ClusterService
from app import db


@bp.route('/clusters', methods=['GET'])
@jwt_required()
def get_clusters():
    """获取集群列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    clusters = Cluster.query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'clusters': [cluster.to_dict() for cluster in clusters.items],
        'total': clusters.total,
        'pages': clusters.pages,
        'current_page': page
    })


@bp.route('/clusters/<int:cluster_id>', methods=['GET'])
@jwt_required()
def get_cluster(cluster_id):
    """获取集群详情"""
    cluster = Cluster.query.get_or_404(cluster_id)
    
    return jsonify({
        'cluster': cluster.to_dict()
    })


@bp.route('/clusters', methods=['POST'])
@jwt_required()
def create_cluster():
    """创建集群"""
    data = request.get_json()
    user_id = get_jwt_identity()
    
    # 验证必填字段
    required_fields = ['name', 'api_server']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'error': f'Missing required field: {field}'
            }), 400
    
    # 检查集群名称是否已存在
    if Cluster.query.filter_by(name=data['name']).first():
        return jsonify({
            'error': 'Cluster name already exists'
        }), 400
    
    try:
        cluster = Cluster(
            name=data['name'],
            display_name=data.get('display_name'),
            description=data.get('description'),
            api_server=data['api_server'],
            kubeconfig=data.get('kubeconfig'),
            cluster_type=data.get('cluster_type'),
            cloud_provider=data.get('cloud_provider'),
            region=data.get('region')
        )
        
        db.session.add(cluster)
        db.session.commit()
        
        # 记录审计日志
        AuditLog.log_action(
            user_id=user_id,
            action='create',
            resource_type='cluster',
            resource_id=str(cluster.id),
            resource_name=cluster.name,
            status='success',
            new_values=cluster.to_dict()
        )
        
        return jsonify({
            'message': 'Cluster created successfully',
            'cluster': cluster.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': f'Failed to create cluster: {str(e)}'
        }), 500


@bp.route('/clusters/<int:cluster_id>', methods=['PUT'])
@jwt_required()
def update_cluster(cluster_id):
    """更新集群"""
    cluster = Cluster.query.get_or_404(cluster_id)
    data = request.get_json()
    user_id = get_jwt_identity()
    
    # 保存旧值用于审计
    old_values = cluster.to_dict()
    
    try:
        # 更新字段
        updatable_fields = [
            'display_name', 'description', 'api_server', 'kubeconfig',
            'cluster_type', 'cloud_provider', 'region', 'status'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(cluster, field, data[field])
        
        db.session.commit()
        
        # 记录审计日志
        AuditLog.log_action(
            user_id=user_id,
            action='update',
            resource_type='cluster',
            resource_id=str(cluster.id),
            resource_name=cluster.name,
            status='success',
            old_values=old_values,
            new_values=cluster.to_dict()
        )
        
        return jsonify({
            'message': 'Cluster updated successfully',
            'cluster': cluster.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': f'Failed to update cluster: {str(e)}'
        }), 500


@bp.route('/clusters/<int:cluster_id>', methods=['DELETE'])
@jwt_required()
def delete_cluster(cluster_id):
    """删除集群"""
    cluster = Cluster.query.get_or_404(cluster_id)
    user_id = get_jwt_identity()
    
    try:
        cluster_data = cluster.to_dict()
        
        db.session.delete(cluster)
        db.session.commit()
        
        # 记录审计日志
        AuditLog.log_action(
            user_id=user_id,
            action='delete',
            resource_type='cluster',
            resource_id=str(cluster_id),
            resource_name=cluster_data['name'],
            status='success',
            old_values=cluster_data
        )
        
        return jsonify({
            'message': 'Cluster deleted successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': f'Failed to delete cluster: {str(e)}'
        }), 500

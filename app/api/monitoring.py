"""
监控管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp


@bp.route('/monitoring/alerts', methods=['GET'])
@jwt_required()
def get_alerts():
    """获取告警列表"""
    return jsonify({
        'message': 'Monitoring alerts API endpoint',
        'status': 'under_development'
    })


@bp.route('/monitoring/metrics', methods=['GET'])
@jwt_required()
def get_metrics():
    """获取指标数据"""
    return jsonify({
        'message': 'Monitoring metrics API endpoint',
        'status': 'under_development'
    })

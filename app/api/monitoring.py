"""
监控管理 API
"""
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import bp
from app.models.monitoring import Alert, Event
from app.services.monitoring_service import MonitoringService


@bp.route('/monitoring/clusters/<int:cluster_id>/metrics', methods=['GET'])
@jwt_required()
def get_cluster_metrics(cluster_id):
    """获取集群指标"""
    time_range = request.args.get('time_range', '1h')

    monitoring_service = MonitoringService()
    result = monitoring_service.get_cluster_metrics(cluster_id, time_range)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/monitoring/clusters/<int:cluster_id>/nodes/<node_name>/metrics', methods=['GET'])
@jwt_required()
def get_node_metrics(cluster_id, node_name):
    """获取节点指标"""
    time_range = request.args.get('time_range', '1h')

    monitoring_service = MonitoringService()
    result = monitoring_service.get_node_metrics(cluster_id, node_name, time_range)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/monitoring/applications/<int:app_id>/metrics', methods=['GET'])
@jwt_required()
def get_application_metrics(app_id):
    """获取应用指标"""
    time_range = request.args.get('time_range', '1h')

    monitoring_service = MonitoringService()
    result = monitoring_service.get_application_metrics(app_id, time_range)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/monitoring/alerts', methods=['GET'])
@jwt_required()
def get_alerts():
    """获取告警列表"""
    cluster_id = request.args.get('cluster_id', type=int)
    severity = request.args.get('severity')
    status = request.args.get('status')
    limit = request.args.get('limit', 100, type=int)

    monitoring_service = MonitoringService()
    result = monitoring_service.get_alerts(cluster_id, severity, status, limit)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/monitoring/alerts/<int:alert_id>/acknowledge', methods=['POST'])
@jwt_required()
def acknowledge_alert(alert_id):
    """确认告警"""
    data = request.get_json() or {}
    user_id = get_jwt_identity()
    note = data.get('note')

    monitoring_service = MonitoringService()
    result = monitoring_service.acknowledge_alert(alert_id, user_id, note)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'alert': result['alert']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/monitoring/alerts/<int:alert_id>/resolve', methods=['POST'])
@jwt_required()
def resolve_alert(alert_id):
    """解决告警"""
    data = request.get_json() or {}
    user_id = get_jwt_identity()
    note = data.get('note')

    monitoring_service = MonitoringService()
    result = monitoring_service.resolve_alert(alert_id, user_id, note)

    if result['success']:
        return jsonify({
            'message': result['message'],
            'alert': result['alert']
        })
    else:
        return jsonify({
            'error': result['error']
        }), 400


@bp.route('/monitoring/events', methods=['GET'])
@jwt_required()
def get_events():
    """获取事件列表"""
    cluster_id = request.args.get('cluster_id', type=int)
    namespace = request.args.get('namespace')
    event_type = request.args.get('event_type')
    limit = request.args.get('limit', 100, type=int)

    monitoring_service = MonitoringService()
    result = monitoring_service.get_events(cluster_id, namespace, event_type, limit)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify({
            'error': result['error']
        }), 400

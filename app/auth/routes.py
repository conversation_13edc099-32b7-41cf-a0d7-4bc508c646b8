"""
认证路由
"""
from flask import request, jsonify, current_app
from flask_login import login_user, logout_user, current_user
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from app.auth import bp
from app.models.user import User
from app.models.audit import AuditLog
from app import db


@bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    
    if not data or not data.get('username') or not data.get('password'):
        return jsonify({
            'error': 'Missing username or password'
        }), 400
    
    username = data.get('username')
    password = data.get('password')
    
    # 查找用户
    user = User.query.filter_by(username=username).first()
    
    if not user or not user.check_password(password):
        # 记录登录失败
        AuditLog.log_action(
            user_id=user.id if user else None,
            action='login',
            resource_type='user',
            status='failed',
            error_message='Invalid username or password',
            user_ip=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        return jsonify({
            'error': 'Invalid username or password'
        }), 401
    
    if not user.is_active:
        return jsonify({
            'error': 'Account is disabled'
        }), 401
    
    # 登录成功
    login_user(user)
    
    # 创建 JWT Token
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)
    
    # 更新最后登录时间
    from datetime import datetime
    user.last_login = datetime.utcnow()
    db.session.commit()
    
    # 记录登录成功
    AuditLog.log_action(
        user_id=user.id,
        action='login',
        resource_type='user',
        status='success',
        user_ip=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    
    return jsonify({
        'message': 'Login successful',
        'access_token': access_token,
        'refresh_token': refresh_token,
        'user': user.to_dict()
    })


@bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    user_id = get_jwt_identity()
    logout_user()
    
    # 记录登出
    AuditLog.log_action(
        user_id=user_id,
        action='logout',
        resource_type='user',
        status='success',
        user_ip=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    
    return jsonify({
        'message': 'Logout successful'
    })


@bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新 Token"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user or not user.is_active:
        return jsonify({
            'error': 'User not found or inactive'
        }), 401
    
    access_token = create_access_token(identity=user_id)
    
    return jsonify({
        'access_token': access_token
    })


@bp.route('/profile', methods=['GET'])
@jwt_required()
def profile():
    """获取用户信息"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({
            'error': 'User not found'
        }), 404
    
    return jsonify({
        'user': user.to_dict()
    })


@bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """修改密码"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({
            'error': 'User not found'
        }), 404
    
    data = request.get_json()
    old_password = data.get('old_password')
    new_password = data.get('new_password')
    
    if not old_password or not new_password:
        return jsonify({
            'error': 'Missing old_password or new_password'
        }), 400
    
    if not user.check_password(old_password):
        return jsonify({
            'error': 'Invalid old password'
        }), 400
    
    if len(new_password) < 6:
        return jsonify({
            'error': 'Password must be at least 6 characters long'
        }), 400
    
    # 更新密码
    user.set_password(new_password)
    db.session.commit()
    
    # 记录密码修改
    AuditLog.log_action(
        user_id=user.id,
        action='update',
        resource_type='user',
        resource_id=str(user.id),
        operation='change_password',
        status='success',
        user_ip=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    
    return jsonify({
        'message': 'Password changed successfully'
    })

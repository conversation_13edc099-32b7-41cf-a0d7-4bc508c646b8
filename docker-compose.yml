version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: k8s-manager-postgres
    environment:
      POSTGRES_DB: k8s_manager
      POSTGRES_USER: k8s_user
      POSTGRES_PASSWORD: k8s_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - k8s-manager-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: k8s-manager-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - k8s-manager-network

  # K8s Manager 应用
  app:
    build: .
    container_name: k8s-manager-app
    environment:
      FLASK_ENV: production
      DATABASE_URL: ************************************************/k8s_manager
      REDIS_URL: redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ~/.kube:/root/.kube:ro  # 挂载 kubeconfig
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - k8s-manager-network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: k8s-manager-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./static:/usr/share/nginx/html/static:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - app
    networks:
      - k8s-manager-network

  # Prometheus (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: k8s-manager-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - k8s-manager-network

  # Grafana (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: k8s-manager-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    networks:
      - k8s-manager-network

  # Elasticsearch (可选)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: k8s-manager-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - k8s-manager-network

  # Kibana (可选)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: k8s-manager-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - k8s-manager-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  k8s-manager-network:
    driver: bridge

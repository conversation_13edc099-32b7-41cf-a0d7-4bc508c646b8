# K8s Manager Platform

基于 Python Flask 的 Kubernetes 管理平台，提供多集群管理、应用部署、监控告警、安全合规等功能。

## 功能特性

### 🏗️ 一、集群管理
- **多集群支持**：支持多个集群（多云/混合云）接入
- **自动发现**：集群组件状态自动发现
- **外部集群接入**：支持外部集群接入
- **健康监控**：集群健康监控
- **节点管理**：节点状态、组件运行情况监控
- **告警通知**：自动告警与事件通知
- **可视化拓扑**：可视化节点拓扑图
- **集群升级**：支持自动或手动升级 K8s 版本
- **灾备恢复**：灾备与恢复机制

### 📦 二、应用管理（DevOps）
- **应用部署**：支持 YAML 配置部署、GUI 可视化部署
- **Helm 管理**：Helm Chart 管理与部署
- **GitOps 支持**：支持 GitOps（如 Argo CD）
- **生命周期管理**：启动、停止、滚动更新、回滚
- **环境隔离**：不同环境（开发、测试、生产）隔离
- **配置管理**：配置中心（如 ConfigMap、Secret）管理
- **CI/CD 集成**：与 GitLab CI、Jenkins、Tekton 等工具集成

### 🧑‍💼 三、用户与权限管理（RBAC）
- **用户管理**：支持 LDAP、OAuth、OIDC、企业 AD 等认证
- **角色管理**：用户角色划分（管理员、开发者、运维等）
- **权限控制**：基于命名空间、资源、操作类型的权限控制
- **审计日志**：审计日志审查

### 📊 四、监控与日志管理
- **监控集成**：与 Prometheus、Grafana 集成
- **资源监控**：查看 Pod、Node、Cluster 的资源使用情况
- **日志管理**：与 ELK、EFK、Loki 等日志系统集成
- **日志查询**：支持多维度日志查询
- **告警通知**：日志告警与告警通知（如钉钉、Slack、邮件）

### 🔐 五、安全与合规
- **网络策略**：支持配置 NetworkPolicy
- **网络拓扑**：可视化网络流量拓扑
- **策略控制**：接入 OPA/Gatekeeper 做策略准入控制
- **镜像扫描**：镜像安全扫描（如 Trivy、Clair）
- **审计合规**：审计日志记录用户行为、合规报告

### ⚙️ 六、资源管理
- **命名空间管理**：支持多租户管理
- **资源隔离**：命名空间资源隔离与限额（ResourceQuota）
- **资源配额**：CPU、内存、存储等限制
- **自动扩缩容**：自动扩缩容（HPA/VPA）
- **存储网络**：PVC、StorageClass、Ingress、Service、LoadBalancer 配置

### 📁 七、镜像与仓库管理
- **仓库集成**：与 Harbor、DockerHub、阿里云等镜像仓库对接
- **镜像扫描**：镜像扫描与签名
- **安全检测**：安全扫描、漏洞检测
- **镜像签名**：支持 Notary 镜像签名机制

### 🧪 八、测试与灰度发布
- **灰度发布**：支持 Canary、Blue-Green 发布策略
- **流量控制**：配合 Istio/Linkerd 实现流量控制
- **A/B 测试**：按用户、流量、时间段等维度分配流量

## 技术架构

### 后端技术栈
- **框架**：Flask 2.3+
- **数据库**：PostgreSQL + Redis
- **ORM**：SQLAlchemy
- **认证**：Flask-Login + JWT
- **API**：RESTful API
- **异步任务**：Celery
- **容器化**：Docker + Docker Compose

### 前端技术栈
- **框架**：React + TypeScript
- **UI 库**：Ant Design
- **状态管理**：Redux Toolkit
- **图表**：ECharts
- **网络拓扑**：D3.js

### 集成组件
- **Kubernetes**：kubernetes-python-client
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack
- **镜像扫描**：Trivy
- **Helm**：Helm SDK

## 快速开始

### 环境要求
- Python 3.11+
- PostgreSQL 12+
- Redis 6+
- Docker & Docker Compose
- Kubernetes 集群

### 安装部署

1. **克隆项目**
```bash
git clone <repository-url>
cd python-k8s-manager
```

2. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库、Redis 等连接信息
```

3. **使用 Docker Compose 部署**
```bash
docker-compose up -d
```

4. **初始化数据库**
```bash
docker-compose exec app flask init-db
docker-compose exec app flask init-data
```

5. **访问应用**
- Web 界面：http://localhost
- API 文档：http://localhost/api/docs
- 默认账号：admin / admin123

### 开发环境

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置数据库**
```bash
# 启动 PostgreSQL 和 Redis
docker-compose up -d postgres redis

# 初始化数据库
flask init-db
flask init-data
```

3. **启动开发服务器**
```bash
python k8s_manager.py
```

## 配置说明

### 数据库配置
```bash
DATABASE_URL=postgresql://k8s_user:k8s_password@localhost/k8s_manager
```

### Kubernetes 配置
```bash
KUBECONFIG_PATH=~/.kube/config
```

### 监控配置
```bash
PROMETHEUS_URL=http://localhost:9090
GRAFANA_URL=http://localhost:3000
ELASTICSEARCH_URL=http://localhost:9200
```

## API 文档

### 认证 API
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出
- `POST /auth/refresh` - 刷新 Token

### 集群管理 API
- `GET /api/v1/clusters` - 获取集群列表
- `POST /api/v1/clusters` - 创建集群
- `GET /api/v1/clusters/{id}` - 获取集群详情
- `PUT /api/v1/clusters/{id}` - 更新集群
- `DELETE /api/v1/clusters/{id}` - 删除集群

### 应用管理 API
- `GET /api/v1/applications` - 获取应用列表
- `POST /api/v1/applications` - 创建应用
- `GET /api/v1/applications/{id}` - 获取应用详情
- `PUT /api/v1/applications/{id}` - 更新应用
- `DELETE /api/v1/applications/{id}` - 删除应用

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 文档：[Documentation]

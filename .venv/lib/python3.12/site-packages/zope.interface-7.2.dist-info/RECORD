zope.interface-7.2-py3.12-nspkg.pth,sha256=_l6EZJaefCi1yytmDbFQGX_jfS2gAtFyaDmlK1uFLec,457
zope.interface-7.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
zope.interface-7.2.dist-info/LICENSE.txt,sha256=PmcdsR32h1FswdtbPWXkqjg-rKPCDOo_r1Og9zNdCjw,2070
zope.interface-7.2.dist-info/METADATA,sha256=x5zBu2XqmIcEpOMqSKD2KuTrvs4e4UE8tyW0S4nk-1I,44353
zope.interface-7.2.dist-info/RECORD,,
zope.interface-7.2.dist-info/WHEEL,sha256=stdCwhyjiC6LzHI-fEibiHQtTULEgvfCFF-5kawH9Pw,109
zope.interface-7.2.dist-info/namespace_packages.txt,sha256=QpUHvpO4wIuZDeEgKY8qZCtD-tAukB0fn_f6utzlb98,5
zope.interface-7.2.dist-info/top_level.txt,sha256=QpUHvpO4wIuZDeEgKY8qZCtD-tAukB0fn_f6utzlb98,5
zope/interface/__init__.py,sha256=9Euz0jFaHg3kT84m2HzikMt7s_JIAjM8BlVctyUfNTk,3475
zope/interface/__pycache__/__init__.cpython-312.pyc,,
zope/interface/__pycache__/_compat.cpython-312.pyc,,
zope/interface/__pycache__/_flatten.cpython-312.pyc,,
zope/interface/__pycache__/adapter.cpython-312.pyc,,
zope/interface/__pycache__/advice.cpython-312.pyc,,
zope/interface/__pycache__/declarations.cpython-312.pyc,,
zope/interface/__pycache__/document.cpython-312.pyc,,
zope/interface/__pycache__/exceptions.cpython-312.pyc,,
zope/interface/__pycache__/interface.cpython-312.pyc,,
zope/interface/__pycache__/interfaces.cpython-312.pyc,,
zope/interface/__pycache__/registry.cpython-312.pyc,,
zope/interface/__pycache__/ro.cpython-312.pyc,,
zope/interface/__pycache__/verify.cpython-312.pyc,,
zope/interface/_compat.py,sha256=8FPCPdRAl2Mlkc45-FBvuN8i1kvYLEkIionVgjk2AP0,4413
zope/interface/_flatten.py,sha256=NKviK4ZyLBCjxXvz5voI9pV_ui2SLqUhubbqZkvgEcg,1059
zope/interface/_zope_interface_coptimizations.c,sha256=oCYrpT2efApCO-EbKs71YXyaSohAbTqdWQ7QEh49SNA,71198
zope/interface/_zope_interface_coptimizations.cpython-312-darwin.so,sha256=hiEOowUIuzo1TZOLc3XHQb5Accl_utNWT4mAi3WliaE,78328
zope/interface/adapter.py,sha256=XN9PD8OrRmP1ZJy2gPvAMXtiOnKcHL34doH0HaKHguQ,36647
zope/interface/advice.py,sha256=YftOBzbXuWQ3AuJImMtYv4SYbE7ubJrjodXzTgYR-dk,3918
zope/interface/common/__init__.py,sha256=ho-q_3jeKVq-jO-5xrN9_Q4PR0o8PDwja5NYsO7qTI0,10644
zope/interface/common/__pycache__/__init__.cpython-312.pyc,,
zope/interface/common/__pycache__/builtins.cpython-312.pyc,,
zope/interface/common/__pycache__/collections.cpython-312.pyc,,
zope/interface/common/__pycache__/idatetime.cpython-312.pyc,,
zope/interface/common/__pycache__/interfaces.cpython-312.pyc,,
zope/interface/common/__pycache__/io.cpython-312.pyc,,
zope/interface/common/__pycache__/mapping.cpython-312.pyc,,
zope/interface/common/__pycache__/numbers.cpython-312.pyc,,
zope/interface/common/__pycache__/sequence.cpython-312.pyc,,
zope/interface/common/builtins.py,sha256=WRHf0D-xo2yWu4uP0zvpISdfuwBqwFFYiYuPLNiIYvQ,3135
zope/interface/common/collections.py,sha256=duvGnbFpUHwri5idqhYblEJrdDYuUvL3vIsZtXggQC4,6809
zope/interface/common/idatetime.py,sha256=sn5ccP9CiQAYzn6mptWf05uInZFp9o_DRu9_MaDXM5c,21039
zope/interface/common/interfaces.py,sha256=fT2pigpAZuqcm0bqVVNDlvSWtv9oKfsJ73ewrymUpIE,5908
zope/interface/common/io.py,sha256=if6Yzclu_T7qeUQNsXeIqREqgVTu-rjYB_VGZfYyz3Y,1242
zope/interface/common/mapping.py,sha256=XI9lO7Uwt_9sWDFyhDIk0Rjcr5nlu7SlD2KDyqdupdw,4687
zope/interface/common/numbers.py,sha256=D4kUnF5OgAk6CKcxaW86VH_OPLh1OXt_cF6WaOfvjLk,1682
zope/interface/common/sequence.py,sha256=SYvqSZpKRaX11vCADK6zGn0LqogPQ0u7INbqgTJ4sg8,5531
zope/interface/common/tests/__init__.py,sha256=Am8tpD6HZQx6pfW7N22-dloC83-dKLA45NiM9Lx7Xss,5553
zope/interface/common/tests/__pycache__/__init__.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/basemapping.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_builtins.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_collections.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_idatetime.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_import_interfaces.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_io.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_numbers.cpython-312.pyc,,
zope/interface/common/tests/basemapping.py,sha256=iitIJEMm7LlS3vQU51RjoN3AeBUNNf0EGLXi_VklJAs,3768
zope/interface/common/tests/test_builtins.py,sha256=eftbVFY5-e7r310YTuf6LlL1Jl5HJ0EXTWZ2icAmrnY,1485
zope/interface/common/tests/test_collections.py,sha256=FpnJI74M9E9vAzTeQr_bkYl1eOIIb79b-GkM8aN8Qv0,6066
zope/interface/common/tests/test_idatetime.py,sha256=qGr8FDbiVuDZHjyDIKcz7Vz6qNfoFzd9eHejMTfIyAg,1923
zope/interface/common/tests/test_import_interfaces.py,sha256=wCFk-2kc7r-kAeOXZn5th5Qw_6k_-pMvdguk5b4NstM,813
zope/interface/common/tests/test_io.py,sha256=gYJnFT52De1JAJruKK6dzyQIdLgYdQME9Yn6QTn2QyI,1686
zope/interface/common/tests/test_numbers.py,sha256=tPrvkFVYa2kSSxf8_j7XYctupfS4BI-ee_jQ5yU8zRM,1395
zope/interface/declarations.py,sha256=csCgY3nsiTjpkjdHs53DJGoeIGSeI_mUaTISYQB_9dQ,43512
zope/interface/document.py,sha256=mhSCJoxw7Whl-q-EMSkys--z3C7IyJ6TEsZ3psMqyJE,4139
zope/interface/exceptions.py,sha256=lW7SnsOT73LZI8w3aRdCRVUubfSy-CGztchnKqv1-5A,8566
zope/interface/interface.py,sha256=Xl9EzsdJS4zzjAflXYTKwL_AUidwgBbsVDASDjp2HrQ,39770
zope/interface/interfaces.py,sha256=9abPucc_lYWpELDFt28wnbjRVtQ8pS48srV8toZmLgk,50268
zope/interface/registry.py,sha256=eDnfUtTFp1z9f-4F-4ZQ9EATLheAYuCP9_coZ3XLdVw,25812
zope/interface/ro.py,sha256=NeImEPsautGBC1VtQAQ1J1BGXmksw6hoSNWD7gVmEaQ,24577
zope/interface/tests/__init__.py,sha256=Vu2CALgC5nADbRAZFb99LcDSXECcPCNdJDL-nv_6mZA,4242
zope/interface/tests/__pycache__/__init__.cpython-312.pyc,,
zope/interface/tests/__pycache__/advisory_testing.cpython-312.pyc,,
zope/interface/tests/__pycache__/dummy.cpython-312.pyc,,
zope/interface/tests/__pycache__/idummy.cpython-312.pyc,,
zope/interface/tests/__pycache__/m1.cpython-312.pyc,,
zope/interface/tests/__pycache__/odd.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_adapter.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_advice.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_compile_flags.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_declarations.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_document.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_element.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_exceptions.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_interface.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_interfaces.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_odd_declarations.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_registry.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_ro.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_sorting.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_verify.cpython-312.pyc,,
zope/interface/tests/advisory_testing.py,sha256=T2XF0DT8t9zi1JxnvR1u0WXpRtyOn5027VMyEcN7Ldc,900
zope/interface/tests/dummy.py,sha256=dd_cYIiPvYJDrEMQD0m2cl9SpkVMrQzMMu1yglSrnb8,913
zope/interface/tests/idummy.py,sha256=ESC_l4UqjCkiCeXWfcphVGtrP7PoNwwTTGzLpp6Doo4,890
zope/interface/tests/m1.py,sha256=clhSXmtaT8liCnM87Kp0bzAHfxIxr6gUBjXnVuxaUNI,850
zope/interface/tests/odd.py,sha256=nwjmxbHCFuI2UvY3Pwc7CfkmZWfXsn3oCb7r7C34DxU,2967
zope/interface/tests/test_adapter.py,sha256=pB3Y_oXXtdtDsKf0BYlvpW281nT1yendxtzWgWp9o_0,80508
zope/interface/tests/test_advice.py,sha256=gFhlbHvSWuC9kdlBPeXO_8_9ihkSU-ih0pVhoocdp4U,6036
zope/interface/tests/test_compile_flags.py,sha256=wU3vtmqgzll6HMPbzFErMpORoVghrcZ5krIV4x6rfo4,1290
zope/interface/tests/test_declarations.py,sha256=7hlSiT33AN4lzZfFigGyAmf6E9ts6-TgIN0jQb0NB04,83091
zope/interface/tests/test_document.py,sha256=0JIYe0GWvDPkr_IBACPw-8HsHNJyNNHBU_lUHYOXd9M,17220
zope/interface/tests/test_element.py,sha256=z9Q2hYwPVzMX05NpA_IeiHoAb4qhTiHI57XQI38w4zQ,1120
zope/interface/tests/test_exceptions.py,sha256=hxceYZyLZghgpxm-ABg1k50-H1CYRSTUGMZyiTrElo8,6445
zope/interface/tests/test_interface.py,sha256=KhTcp5nM_K2ffiZSTRzvjhag9tZMfc8qvpV-a3OlSBg,92717
zope/interface/tests/test_interfaces.py,sha256=l4SCfUmVfBO1UoZLsTOOvDyoYUM7Wex3J67uR7taFpQ,4395
zope/interface/tests/test_odd_declarations.py,sha256=0DBSBIoOUdSRcDnhiScexGhZ2mVydYWnlJOBvny76Uo,7660
zope/interface/tests/test_registry.py,sha256=8CyxRkJ46ITauSGp86y1gSN4KHvZNVPTVKgjD13aXBk,112599
zope/interface/tests/test_ro.py,sha256=CvuZne2GdQu0JA4WQObKm8mG-F7__3T4VBINwoj3QQI,14465
zope/interface/tests/test_sorting.py,sha256=I4P2jvStUzYrNOnIQgkZZs0sTn9BHe7g5RLQlFmTTW4,2051
zope/interface/tests/test_verify.py,sha256=JnVLFlHGgVn8fC6u-WPW-mDtfmSuDA2PC0sglWuVBjI,19200
zope/interface/verify.py,sha256=cEaIWPcS6gT97RkQW2SQJxxuUP7wRrwYwsJtXqo8jdE,7333

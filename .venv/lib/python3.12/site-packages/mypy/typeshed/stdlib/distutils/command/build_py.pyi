from typing import Any

from ..cmd import Command
from ..util import Mixin2to3 as Mixin2to3

class build_py(Command):
    description: str
    user_options: Any
    boolean_options: Any
    negative_opt: Any
    build_lib: Any
    py_modules: Any
    package: Any
    package_data: Any
    package_dir: Any
    compile: int
    optimize: int
    force: Any
    def initialize_options(self) -> None: ...
    packages: Any
    data_files: Any
    def finalize_options(self) -> None: ...
    def run(self) -> None: ...
    def get_data_files(self): ...
    def find_data_files(self, package, src_dir): ...
    def build_package_data(self) -> None: ...
    def get_package_dir(self, package): ...
    def check_package(self, package, package_dir): ...
    def check_module(self, module, module_file): ...
    def find_package_modules(self, package, package_dir): ...
    def find_modules(self): ...
    def find_all_modules(self): ...
    def get_source_files(self): ...
    def get_module_outfile(self, build_dir, package, module): ...
    def get_outputs(self, include_bytecode: int = 1): ...
    def build_module(self, module, module_file, package): ...
    def build_modules(self) -> None: ...
    def build_packages(self) -> None: ...
    def byte_compile(self, files) -> None: ...

class build_py_2to3(build_py, Mixin2to3):
    updated_files: Any
    def run(self) -> None: ...
    def build_module(self, module, module_file, package): ...

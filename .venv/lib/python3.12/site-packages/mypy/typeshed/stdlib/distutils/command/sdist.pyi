from typing import Any

from ..cmd import Command

def show_formats() -> None: ...

class sdist(Command):
    description: str
    def checking_metadata(self): ...
    user_options: Any
    boolean_options: Any
    help_options: Any
    negative_opt: Any
    sub_commands: Any
    READMES: Any
    template: Any
    manifest: Any
    use_defaults: int
    prune: int
    manifest_only: int
    force_manifest: int
    formats: Any
    keep_temp: int
    dist_dir: Any
    archive_files: Any
    metadata_check: int
    owner: Any
    group: Any
    def initialize_options(self) -> None: ...
    def finalize_options(self) -> None: ...
    filelist: Any
    def run(self) -> None: ...
    def check_metadata(self) -> None: ...
    def get_file_list(self) -> None: ...
    def add_defaults(self) -> None: ...
    def read_template(self) -> None: ...
    def prune_file_list(self) -> None: ...
    def write_manifest(self) -> None: ...
    def read_manifest(self) -> None: ...
    def make_release_tree(self, base_dir, files) -> None: ...
    def make_distribution(self) -> None: ...
    def get_archive_files(self): ...

[case testTheBigMatch_python3_10]
class Person:
    __match_args__ = ("name", "age")

    name: str
    age: int

    def __init__(self, name: str, age: int) -> None:
        self.name = name
        self.age = age

    def __str__(self) -> str:
        return f"Person(name={self.name!r}, age={self.age})"


def f(x: object) -> None:
    match x:
        case 123:
            print("test 1")

        case 456 | 789:
            print("test 2")

        case True | False | None:
            print("test 3")

        case Person("bob" as name, age):
            print(f"test 4 ({name=}, {age=})")

        case num if num == 5:
            print("test 5")

        case 6 as num:
            print(f"test 6 ({num=})")

        case (7 | "7") as value:
            print(f"test 7 ({value=})")

        case Person("alice", age=123):
            print("test 8")

        case Person("charlie", age=123 | 456):
            print("test 9")

        case Person("dave", 123) as dave:
            print(f"test 10 {dave}")

        case {"test": 11}:
            print("test 11")

        case {"test": 12, **rest}:
            print(f"test 12 (rest={rest})")

        case {}:
            print("test map final")

        case ["test", 13]:
            print("test 13")

        case ["test", 13, _]:
            print("test 13b")

        case ["test", 14, *_]:
            print("test 14")

        # TODO: Fix "rest" being used here coliding with above "rest"
        case ["test", 15, *rest2]:
            print(f"test 15 ({rest2})")

        case ["test", *rest3, 16]:
            print(f"test 16 ({rest3})")

        case [*rest4, "test", 17]:
            print(f"test 17 ({rest4})")

        case [*rest4, "test", 18, "some", "fluff"]:
            print(f"test 18 ({rest4})")

        case str("test 19"):
            print("test 19")

        case str(test_20) if test_20.startswith("test 20"):
            print(f"test 20 ({test_20[7:]!r})")

        case ("test 21" as value) | ("test 21 as well" as value):
            print(f"test 21 ({value[7:]!r})")

        case []:
            print("test sequence final")

        case _:
            print("test final")
[file driver.py]
from native import f, Person

# test 1
f(123)

# test 2
f(456)
f(789)

# test 3
f(True)
f(False)
f(None)

# test 4
f(Person("bob", 123))

# test 5
f(5)

# test 6
f(6)

# test 7
f(7)
f("7")

# test 8
f(Person("alice", 123))

# test 9
f(Person("charlie", 123))
f(Person("charlie", 456))

# test 10
f(Person("dave", 123))

# test 11
f({"test": 11})
f({"test": 11, "some": "key"})

# test 12
f({"test": 12})
f({"test": 12, "key": "value"})
f({"test": 12, "key": "value", "abc": "123"})

# test map final
f({})

# test 13
f(["test", 13])

# test 13b
f(["test", 13, "fail"])

# test 14
f(["test", 14])
f(["test", 14, "something"])

# test 15
f(["test", 15])
f(["test", 15, "something"])

# test 16
f(["test", 16])
f(["test", "filler", 16])
f(["test", "more", "filler", 16])

# test 17
f(["test", 17])
f(["stuff", "test", 17])
f(["more", "stuff", "test", 17])

# test 18
f(["test", 18, "some", "fluff"])
f(["stuff", "test", 18, "some", "fluff"])
f(["more", "stuff", "test", 18, "some", "fluff"])

# test 19
f("test 19")

# test 20
f("test 20")
f("test 20 something else")

# test 21
f("test 21")
f("test 21 as well")

# test sequence final
f([])

# test final
f("")

[out]
test 1
test 2
test 2
test 3
test 3
test 3
test 4 (name='bob', age=123)
test 5
test 6 (num=6)
test 7 (value=7)
test 7 (value='7')
test 8
test 9
test 9
test 10 Person(name='dave', age=123)
test 11
test 11
test 12 (rest={})
test 12 (rest={'key': 'value'})
test 12 (rest={'key': 'value', 'abc': '123'})
test map final
test 13
test 13b
test 14
test 14
test 15 ([])
test 15 (['something'])
test 16 ([])
test 16 (['filler'])
test 16 (['more', 'filler'])
test 17 ([])
test 17 (['stuff'])
test 17 (['more', 'stuff'])
test 18 ([])
test 18 (['stuff'])
test 18 (['more', 'stuff'])
test 19
test 20 ('')
test 20 (' something else')
test 21 ('')
test 21 (' as well')
test sequence final
test final
[case testCustomMappingAndSequenceObjects_python3_10]
def f(x: object) -> None:
    match x:
        case {"key": "value", **rest}:
            print(rest, type(rest))

        case [1, 2, *rest2]:
            print(rest2, type(rest2))

[file driver.py]
from collections.abc import Mapping, Sequence

from native import f

class CustomMapping(Mapping):
    inner: dict

    def __init__(self, inner: dict) -> None:
        self.inner = inner

    def __getitem__(self, key):
        return self.inner[key]

    def __iter__(self):
        return iter(self.inner)

    def __len__(self) -> int:
        return len(self.inner)


class CustomSequence(Sequence):
    inner: list

    def __init__(self, inner: list) -> None:
        self.inner = inner

    def __getitem__(self, index: int) -> None:
        return self.inner[index]

    def __len__(self) -> int:
        return len(self.inner)

mapping = CustomMapping({"key": "value", "some": "data"})
sequence = CustomSequence([1, 2, 3])

f(mapping)
f(sequence)

[out]
{'some': 'data'} <class 'dict'>
[3] <class 'list'>

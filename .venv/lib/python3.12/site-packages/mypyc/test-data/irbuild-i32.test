# Test cases for i32 native ints. Focus on things that are different from i64; no need to
# duplicate all i64 test cases here.

[case testI32BinaryOp]
from mypy_extensions import i32

def add_op(x: i32, y: i32) -> i32:
    x = y + x
    y = x + 5
    y += x
    y += 7
    x = 5 + y
    return x
def compare(x: i32, y: i32) -> None:
    a = x == y
    b = x == -5
    c = x < y
    d = x < -5
    e = -5 == x
    f = -5 < x
[out]
def add_op(x, y):
    x, y, r0, r1, r2, r3, r4 :: i32
L0:
    r0 = y + x
    x = r0
    r1 = x + 5
    y = r1
    r2 = y + x
    y = r2
    r3 = y + 7
    y = r3
    r4 = 5 + y
    x = r4
    return x
def compare(x, y):
    x, y :: i32
    r0 :: bit
    a :: bool
    r1 :: bit
    b :: bool
    r2 :: bit
    c :: bool
    r3 :: bit
    d :: bool
    r4 :: bit
    e :: bool
    r5 :: bit
    f :: bool
L0:
    r0 = x == y
    a = r0
    r1 = x == -5
    b = r1
    r2 = x < y :: signed
    c = r2
    r3 = x < -5 :: signed
    d = r3
    r4 = -5 == x
    e = r4
    r5 = -5 < x :: signed
    f = r5
    return 1

[case testI32UnaryOp]
from mypy_extensions import i32

def unary(x: i32) -> i32:
    y = -x
    x = ~y
    y = +x
    return y
[out]
def unary(x):
    x, r0, y, r1 :: i32
L0:
    r0 = 0 - x
    y = r0
    r1 = y ^ -1
    x = r1
    y = x
    return y

[case testI32DivisionByConstant]
from mypy_extensions import i32

def div_by_constant(x: i32) -> i32:
    x = x // 5
    x //= 17
    return x
[out]
def div_by_constant(x):
    x, r0, r1 :: i32
    r2, r3, r4 :: bit
    r5 :: i32
    r6 :: bit
    r7, r8, r9 :: i32
    r10, r11, r12 :: bit
    r13 :: i32
    r14 :: bit
    r15 :: i32
L0:
    r0 = x / 5
    r1 = r0
    r2 = x < 0 :: signed
    r3 = 5 < 0 :: signed
    r4 = r2 == r3
    if r4 goto L3 else goto L1 :: bool
L1:
    r5 = r1 * 5
    r6 = r5 == x
    if r6 goto L3 else goto L2 :: bool
L2:
    r7 = r1 - 1
    r1 = r7
L3:
    x = r1
    r8 = x / 17
    r9 = r8
    r10 = x < 0 :: signed
    r11 = 17 < 0 :: signed
    r12 = r10 == r11
    if r12 goto L6 else goto L4 :: bool
L4:
    r13 = r9 * 17
    r14 = r13 == x
    if r14 goto L6 else goto L5 :: bool
L5:
    r15 = r9 - 1
    r9 = r15
L6:
    x = r9
    return x

[case testI32ModByConstant]
from mypy_extensions import i32

def mod_by_constant(x: i32) -> i32:
    x = x % 5
    x %= 17
    return x
[out]
def mod_by_constant(x):
    x, r0, r1 :: i32
    r2, r3, r4, r5 :: bit
    r6, r7, r8 :: i32
    r9, r10, r11, r12 :: bit
    r13 :: i32
L0:
    r0 = x % 5
    r1 = r0
    r2 = x < 0 :: signed
    r3 = 5 < 0 :: signed
    r4 = r2 == r3
    if r4 goto L3 else goto L1 :: bool
L1:
    r5 = r1 == 0
    if r5 goto L3 else goto L2 :: bool
L2:
    r6 = r1 + 5
    r1 = r6
L3:
    x = r1
    r7 = x % 17
    r8 = r7
    r9 = x < 0 :: signed
    r10 = 17 < 0 :: signed
    r11 = r9 == r10
    if r11 goto L6 else goto L4 :: bool
L4:
    r12 = r8 == 0
    if r12 goto L6 else goto L5 :: bool
L5:
    r13 = r8 + 17
    r8 = r13
L6:
    x = r8
    return x

[case testI32DivModByVariable]
from mypy_extensions import i32

def divmod(x: i32, y: i32) -> i32:
    a = x // y
    return a % y
[out]
def divmod(x, y):
    x, y, r0, a, r1 :: i32
L0:
    r0 = CPyInt32_Divide(x, y)
    a = r0
    r1 = CPyInt32_Remainder(a, y)
    return r1

[case testI32BoxAndUnbox]
from typing import Any
from mypy_extensions import i32

def f(x: Any) -> Any:
    y: i32 = x
    return y
[out]
def f(x):
    x :: object
    r0, y :: i32
    r1 :: object
L0:
    r0 = unbox(i32, x)
    y = r0
    r1 = box(i32, y)
    return r1

[case testI32MixedCompare1_64bit]
from mypy_extensions import i32
def f(x: int, y: i32) -> bool:
    return x == y
[out]
def f(x, y):
    x :: int
    y :: i32
    r0 :: native_int
    r1, r2, r3 :: bit
    r4 :: native_int
    r5, r6 :: i32
    r7 :: bit
L0:
    r0 = x & 1
    r1 = r0 == 0
    if r1 goto L1 else goto L4 :: bool
L1:
    r2 = x < 4294967296 :: signed
    if r2 goto L2 else goto L4 :: bool
L2:
    r3 = x >= -4294967296 :: signed
    if r3 goto L3 else goto L4 :: bool
L3:
    r4 = x >> 1
    r5 = truncate r4: native_int to i32
    r6 = r5
    goto L5
L4:
    CPyInt32_Overflow()
    unreachable
L5:
    r7 = r6 == y
    return r7

[case testI32MixedCompare2_64bit]
from mypy_extensions import i32
def f(x: i32, y: int) -> bool:
    return x == y
[out]
def f(x, y):
    x :: i32
    y :: int
    r0 :: native_int
    r1, r2, r3 :: bit
    r4 :: native_int
    r5, r6 :: i32
    r7 :: bit
L0:
    r0 = y & 1
    r1 = r0 == 0
    if r1 goto L1 else goto L4 :: bool
L1:
    r2 = y < 4294967296 :: signed
    if r2 goto L2 else goto L4 :: bool
L2:
    r3 = y >= -4294967296 :: signed
    if r3 goto L3 else goto L4 :: bool
L3:
    r4 = y >> 1
    r5 = truncate r4: native_int to i32
    r6 = r5
    goto L5
L4:
    CPyInt32_Overflow()
    unreachable
L5:
    r7 = x == r6
    return r7

[case testI32MixedCompare_32bit]
from mypy_extensions import i32
def f(x: int, y: i32) -> bool:
    return x == y
[out]
def f(x, y):
    x :: int
    y :: i32
    r0 :: native_int
    r1 :: bit
    r2, r3 :: i32
    r4 :: ptr
    r5 :: c_ptr
    r6 :: i32
    r7 :: bit
L0:
    r0 = x & 1
    r1 = r0 == 0
    if r1 goto L1 else goto L2 :: bool
L1:
    r2 = x >> 1
    r3 = r2
    goto L3
L2:
    r4 = x ^ 1
    r5 = r4
    r6 = CPyLong_AsInt32(r5)
    r3 = r6
    keep_alive x
L3:
    r7 = r3 == y
    return r7

[case testI32ConvertToInt_64bit]
from mypy_extensions import i32

def i32_to_int(a: i32) -> int:
    return a
[out]
def i32_to_int(a):
    a :: i32
    r0 :: native_int
    r1 :: int
L0:
    r0 = extend signed a: i32 to native_int
    r1 = r0 << 1
    return r1

[case testI32ConvertToInt_32bit]
from mypy_extensions import i32

def i32_to_int(a: i32) -> int:
    return a
[out]
def i32_to_int(a):
    a :: i32
    r0, r1 :: bit
    r2, r3, r4 :: int
L0:
    r0 = a <= 1073741823 :: signed
    if r0 goto L1 else goto L2 :: bool
L1:
    r1 = a >= -1073741824 :: signed
    if r1 goto L3 else goto L2 :: bool
L2:
    r2 = CPyTagged_FromSsize_t(a)
    r3 = r2
    goto L4
L3:
    r4 = a << 1
    r3 = r4
L4:
    return r3

[case testI32OperatorAssignmentMixed_64bit]
from mypy_extensions import i32

def f(a: i32) -> None:
    x = 0
    x += a
[out]
def f(a):
    a :: i32
    x :: int
    r0 :: native_int
    r1, r2, r3 :: bit
    r4 :: native_int
    r5, r6, r7 :: i32
    r8 :: native_int
    r9 :: int
L0:
    x = 0
    r0 = x & 1
    r1 = r0 == 0
    if r1 goto L1 else goto L4 :: bool
L1:
    r2 = x < 4294967296 :: signed
    if r2 goto L2 else goto L4 :: bool
L2:
    r3 = x >= -4294967296 :: signed
    if r3 goto L3 else goto L4 :: bool
L3:
    r4 = x >> 1
    r5 = truncate r4: native_int to i32
    r6 = r5
    goto L5
L4:
    CPyInt32_Overflow()
    unreachable
L5:
    r7 = r6 + a
    r8 = extend signed r7: i32 to native_int
    r9 = r8 << 1
    x = r9
    return 1

[case testI32InitializeFromLiteral]
from mypy_extensions import i32, i64

def f() -> None:
    x: i32 = 0
    y: i32 = -127
    z: i32 = 5 + 7
[out]
def f():
    x, y, z :: i32
L0:
    x = 0
    y = -127
    z = 12
    return 1

[case testI32ExplicitConversionFromNativeInt]
from mypy_extensions import i64, i32, i16

def from_i16(x: i16) -> i32:
    return i32(x)

def from_i32(x: i32) -> i32:
    return i32(x)

def from_i64(x: i64) -> i32:
    return i32(x)
[out]
def from_i16(x):
    x :: i16
    r0 :: i32
L0:
    r0 = extend signed x: i16 to i32
    return r0
def from_i32(x):
    x :: i32
L0:
    return x
def from_i64(x):
    x :: i64
    r0 :: i32
L0:
    r0 = truncate x: i64 to i32
    return r0

[case testI32ExplicitConversionFromInt_64bit]
from mypy_extensions import i32

def f(x: int) -> i32:
    return i32(x)
[out]
def f(x):
    x :: int
    r0 :: native_int
    r1, r2, r3 :: bit
    r4 :: native_int
    r5, r6 :: i32
L0:
    r0 = x & 1
    r1 = r0 == 0
    if r1 goto L1 else goto L4 :: bool
L1:
    r2 = x < 4294967296 :: signed
    if r2 goto L2 else goto L4 :: bool
L2:
    r3 = x >= -4294967296 :: signed
    if r3 goto L3 else goto L4 :: bool
L3:
    r4 = x >> 1
    r5 = truncate r4: native_int to i32
    r6 = r5
    goto L5
L4:
    CPyInt32_Overflow()
    unreachable
L5:
    return r6

[case testI32ExplicitConversionFromLiteral_64bit]
from mypy_extensions import i32

def f() -> None:
    x = i32(0)
    y = i32(11)
    z = i32(-3)
    a = i32(2**31)
[out]
def f():
    x, y, z, a :: i32
L0:
    x = 0
    y = 11
    z = -3
    a = -2147483648
    return 1

[case testI32ExplicitConversionFromVariousTypes_64bit]
from mypy_extensions import i32

def bool_to_i32(b: bool) -> i32:
    return i32(b)

def str_to_i32(s: str) -> i32:
    return i32(s)

class C:
    def __int__(self) -> i32:
        return 5

def instance_to_i32(c: C) -> i32:
    return i32(c)

def float_to_i32(x: float) -> i32:
    return i32(x)
[out]
def bool_to_i32(b):
    b :: bool
    r0 :: i32
L0:
    r0 = extend b: builtins.bool to i32
    return r0
def str_to_i32(s):
    s :: str
    r0 :: object
    r1 :: i32
L0:
    r0 = CPyLong_FromStr(s)
    r1 = unbox(i32, r0)
    return r1
def C.__int__(self):
    self :: __main__.C
L0:
    return 5
def instance_to_i32(c):
    c :: __main__.C
    r0 :: i32
L0:
    r0 = c.__int__()
    return r0
def float_to_i32(x):
    x :: float
    r0 :: int
    r1 :: native_int
    r2, r3, r4 :: bit
    r5 :: native_int
    r6, r7 :: i32
L0:
    r0 = CPyTagged_FromFloat(x)
    r1 = r0 & 1
    r2 = r1 == 0
    if r2 goto L1 else goto L4 :: bool
L1:
    r3 = r0 < 4294967296 :: signed
    if r3 goto L2 else goto L4 :: bool
L2:
    r4 = r0 >= -4294967296 :: signed
    if r4 goto L3 else goto L4 :: bool
L3:
    r5 = r0 >> 1
    r6 = truncate r5: native_int to i32
    r7 = r6
    goto L5
L4:
    CPyInt32_Overflow()
    unreachable
L5:
    return r7

[case testI32ExplicitConversionFromFloat_32bit]
from mypy_extensions import i32

def float_to_i32(x: float) -> i32:
    return i32(x)
[out]
def float_to_i32(x):
    x :: float
    r0 :: int
    r1 :: native_int
    r2 :: bit
    r3, r4 :: i32
    r5 :: ptr
    r6 :: c_ptr
    r7 :: i32
L0:
    r0 = CPyTagged_FromFloat(x)
    r1 = r0 & 1
    r2 = r1 == 0
    if r2 goto L1 else goto L2 :: bool
L1:
    r3 = r0 >> 1
    r4 = r3
    goto L3
L2:
    r5 = r0 ^ 1
    r6 = r5
    r7 = CPyLong_AsInt32(r6)
    r4 = r7
    keep_alive r0
L3:
    return r4

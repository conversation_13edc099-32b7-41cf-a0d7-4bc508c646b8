[case testIntConstantFolding]
def bin_ops() -> None:
    add = 15 + 47
    add_mul = (2 + 3) * 5
    sub = 7 - 11
    div = 3 / 2
    bit_and = 6 & 10
    bit_or = 6 | 10
    bit_xor = 6 ^ 10
    lshift = 5 << 2
    rshift = 13 >> 2
    lshift0 = 5 << 0
    rshift0 = 13 >> 0
def unary_ops() -> None:
    neg1 = -5
    neg2 = --1
    neg3 = -0
    pos = +5
    inverted1 = ~0
    inverted2 = ~5
    inverted3 = ~3
def pow() -> None:
    p0 = 3**0
    p1 = 3**5
    p2 = (-5)**3
    p3 = 0**0
[out]
def bin_ops():
    add, add_mul, sub :: int
    div :: float
    bit_and, bit_or, bit_xor, lshift, rshift, lshift0, rshift0 :: int
L0:
    add = 124
    add_mul = 50
    sub = -8
    div = 1.5
    bit_and = 4
    bit_or = 28
    bit_xor = 24
    lshift = 40
    rshift = 6
    lshift0 = 10
    rshift0 = 26
    return 1
def unary_ops():
    neg1, neg2, neg3, pos, inverted1, inverted2, inverted3 :: int
L0:
    neg1 = -10
    neg2 = 2
    neg3 = 0
    pos = 10
    inverted1 = -2
    inverted2 = -12
    inverted3 = -8
    return 1
def pow():
    p0, p1, p2, p3 :: int
L0:
    p0 = 2
    p1 = 486
    p2 = -250
    p3 = 2
    return 1

[case testIntConstantFoldingDivMod]
def div() -> None:
    div1 = 25 // 5
    div2 = 24 // 5
    div3 = 29 // 5
    div4 = 30 // 5
    div_zero = 0 // 5
    neg1 = -1 // 3
    neg2 = -2 // 3
    neg3 = -3 // 3
    neg4 = -4 // 3
    neg_neg = -765467 // -234
    pos_neg = 983745 // -7864
def mod() -> None:
    mod1 = 25 % 5
    mod2 = 24 % 5
    mod3 = 29 % 5
    mod4 = 30 % 5
    mod_zero = 0 % 5
    neg1 = -4 % 3
    neg2 = -5 % 3
    neg3 = -6 % 3
    neg4 = -7 % 3
    neg_neg = -765467 % -234
    pos_neg = 983745 % -7864
[out]
def div():
    div1, div2, div3, div4, div_zero, neg1, neg2, neg3, neg4, neg_neg, pos_neg :: int
L0:
    div1 = 10
    div2 = 8
    div3 = 10
    div4 = 12
    div_zero = 0
    neg1 = -2
    neg2 = -2
    neg3 = -2
    neg4 = -4
    neg_neg = 6542
    pos_neg = -252
    return 1
def mod():
    mod1, mod2, mod3, mod4, mod_zero, neg1, neg2, neg3, neg4, neg_neg, pos_neg :: int
L0:
    mod1 = 0
    mod2 = 8
    mod3 = 8
    mod4 = 0
    mod_zero = 0
    neg1 = 4
    neg2 = 2
    neg3 = 0
    neg4 = 4
    neg_neg = -106
    pos_neg = -14238
    return 1

[case testIntConstantFoldingUnsupportedCases]
def error_cases() -> None:
    div_by_zero = 5 / 0
    floor_div_by_zero = 5 // 0
    mod_by_zero = 5 % 0
    lshift_neg = 6 << -1
    rshift_neg = 7 >> -1
def unsupported_pow() -> None:
    p = 3 ** (-1)
[out]
def error_cases():
    r0, div_by_zero :: float
    r1, floor_div_by_zero, r2, mod_by_zero, r3, lshift_neg, r4, rshift_neg :: int
L0:
    r0 = CPyTagged_TrueDivide(10, 0)
    div_by_zero = r0
    r1 = CPyTagged_FloorDivide(10, 0)
    floor_div_by_zero = r1
    r2 = CPyTagged_Remainder(10, 0)
    mod_by_zero = r2
    r3 = CPyTagged_Lshift(12, -2)
    lshift_neg = r3
    r4 = CPyTagged_Rshift(14, -2)
    rshift_neg = r4
    return 1
def unsupported_pow():
    r0, r1, r2 :: object
    r3, p :: float
L0:
    r0 = object 3
    r1 = object -1
    r2 = CPyNumber_Power(r0, r1)
    r3 = unbox(float, r2)
    p = r3
    return 1

[case testIntConstantFoldingBigIntResult_64bit]
def long_and_short() -> None:
    # The smallest and largest representable short integers
    short1 =  0x3ffffffffffffff0 + 0xf  # (1 << 62) - 1
    short2 = -0x3fffffffffffffff - 1    # -(1 << 62)
    short3 = -0x4000000000000000
    # Smallest big integers by absolute value
    big1 = 1 << 62
    big2 = 0x4000000000000000  # 1 << 62
    big3 = -(1 << 62) - 1
    big4 = -0x4000000000000001  # -(1 << 62) - 1
    big5 = 123**41
[out]
def long_and_short():
    short1, short2, short3, r0, big1, r1, big2, r2, big3, r3, big4, r4, big5 :: int
L0:
    short1 = 9223372036854775806
    short2 = -9223372036854775808
    short3 = -9223372036854775808
    r0 = object 4611686018427387904
    big1 = r0
    r1 = object 4611686018427387904
    big2 = r1
    r2 = object -4611686018427387905
    big3 = r2
    r3 = object -4611686018427387905
    big4 = r3
    r4 = object 48541095000524544750127162673405880068636916264012200797813591925035550682238127143323
    big5 = r4
    return 1

[case testIntConstantFoldingFinal]
from typing_extensions import Final
X: Final = 5
Y: Final = 2 + 4

def f() -> None:
    a = X + 1
    a = Y + 1
[out]
def f():
    a :: int
L0:
    a = 12
    a = 14
    return 1

[case testIntConstantFoldingClassFinal]
from typing_extensions import Final
class C:
    X: Final = 5

def f() -> None:
    a = C.X + 1
[out]
def C.__mypyc_defaults_setup(__mypyc_self__):
    __mypyc_self__ :: __main__.C
L0:
    __mypyc_self__.X = 10
    return 1
def f():
    a :: int
L0:
    a = 12
    return 1

[case testFloatConstantFolding]
from typing_extensions import Final

N: Final = 1.5
N2: Final = 1.5 * 2

def bin_ops() -> None:
    add = 0.5 + 0.5
    add_mul = (1.5 + 3.5) * 5.0
    sub = 7.0 - 7.5
    div = 3.0 / 2.0
    floor_div = 3.0 // 2.0
def bin_ops_neg() -> None:
    add = 0.5 + -0.5
    add_mul = (-1.5 + 3.5) * -5.0
    add_mul2 = (1.5 + -3.5) * -5.0
    sub = 7.0 - -7.5
    div = 3.0 / -2.0
    floor_div = 3.0 // -2.0
def unary_ops() -> None:
    neg1 = -5.5
    neg2 = --1.5
    neg3 = -0.0
    pos = +5.5
def pow() -> None:
    p0 = 16.0**0
    p1 = 16.0**0.5
    p2 = (-5.0)**3
    p3 = 16.0**(-0)
    p4 = 16.0**(-0.5)
    p5 = (-2.0)**(-1)
def error_cases() -> None:
    div = 2.0 / 0.0
    floor_div = 2.0 // 0.0
    power_imag = (-2.0)**0.5
    power_imag2 = (-2.0)**(-0.5)
    power_overflow = 2.0**10000.0
def final_floats() -> None:
    add1 = N + 1.2
    add2 = N + N2
    add3 = -1.2 + N2
[out]
def bin_ops():
    add, add_mul, sub, div, floor_div :: float
L0:
    add = 1.0
    add_mul = 25.0
    sub = -0.5
    div = 1.5
    floor_div = 1.0
    return 1
def bin_ops_neg():
    add, add_mul, add_mul2, sub, div, floor_div :: float
L0:
    add = 0.0
    add_mul = -10.0
    add_mul2 = 10.0
    sub = 14.5
    div = -1.5
    floor_div = -2.0
    return 1
def unary_ops():
    neg1, neg2, neg3, pos :: float
L0:
    neg1 = -5.5
    neg2 = 1.5
    neg3 = -0.0
    pos = 5.5
    return 1
def pow():
    p0, p1, p2, p3, p4, p5 :: float
L0:
    p0 = 1.0
    p1 = 4.0
    p2 = -125.0
    p3 = 1.0
    p4 = 0.25
    p5 = -0.5
    return 1
def error_cases():
    r0 :: bit
    r1 :: bool
    r2, div, r3, floor_div :: float
    r4, r5, r6 :: object
    r7, power_imag :: float
    r8, r9, r10 :: object
    r11, power_imag2 :: float
    r12, r13, r14 :: object
    r15, power_overflow :: float
L0:
    r0 = 0.0 == 0.0
    if r0 goto L1 else goto L2 :: bool
L1:
    r1 = raise ZeroDivisionError('float division by zero')
    unreachable
L2:
    r2 = 2.0 / 0.0
    div = r2
    r3 = CPyFloat_FloorDivide(2.0, 0.0)
    floor_div = r3
    r4 = box(float, -2.0)
    r5 = box(float, 0.5)
    r6 = CPyNumber_Power(r4, r5)
    r7 = unbox(float, r6)
    power_imag = r7
    r8 = box(float, -2.0)
    r9 = box(float, -0.5)
    r10 = CPyNumber_Power(r8, r9)
    r11 = unbox(float, r10)
    power_imag2 = r11
    r12 = box(float, 2.0)
    r13 = box(float, 10000.0)
    r14 = CPyNumber_Power(r12, r13)
    r15 = unbox(float, r14)
    power_overflow = r15
    return 1
def final_floats():
    add1, add2, add3 :: float
L0:
    add1 = 2.7
    add2 = 4.5
    add3 = 1.8
    return 1

[case testMixedFloatIntConstantFolding]
def bin_ops() -> None:
    add = 1 + 0.5
    sub = 1 - 0.5
    mul = 0.5 * 5
    div = 5 / 0.5
    floor_div = 9.5 // 5
def error_cases() -> None:
    div = 2.0 / 0
    floor_div = 2.0 // 0
    power_overflow = 2.0**10000
[out]
def bin_ops():
    add, sub, mul, div, floor_div :: float
L0:
    add = 1.5
    sub = 0.5
    mul = 2.5
    div = 10.0
    floor_div = 1.0
    return 1
def error_cases():
    r0 :: bit
    r1 :: bool
    r2, div, r3, floor_div :: float
    r4, r5, r6 :: object
    r7, power_overflow :: float
L0:
    r0 = 0.0 == 0.0
    if r0 goto L1 else goto L2 :: bool
L1:
    r1 = raise ZeroDivisionError('float division by zero')
    unreachable
L2:
    r2 = 2.0 / 0.0
    div = r2
    r3 = CPyFloat_FloorDivide(2.0, 0.0)
    floor_div = r3
    r4 = box(float, 2.0)
    r5 = box(float, 10000.0)
    r6 = CPyNumber_Power(r4, r5)
    r7 = unbox(float, r6)
    power_overflow = r7
    return 1

[case testStrConstantFolding]
from typing_extensions import Final

S: Final = 'z'
N: Final = 2

def f() -> None:
    x = 'foo' + 'bar'
    y = 'x' + 'y' + S
    mul = "foobar" * 2
    mul2 = N * "foobar"
[out]
def f():
    r0, x, r1, y, r2, mul, r3, mul2 :: str
L0:
    r0 = 'foobar'
    x = r0
    r1 = 'xyz'
    y = r1
    r2 = 'foobarfoobar'
    mul = r2
    r3 = 'foobarfoobar'
    mul2 = r3
    return 1

[case testBytesConstantFolding]
from typing_extensions import Final

N: Final = 2

def f() -> None:
    # Unfortunately, mypy doesn't store the bytes value of final refs.
    x = b'foo' + b'bar'
    mul = b"foobar" * 2
    mul2 = N * b"foobar"
[out]
def f():
    r0, x, r1, mul, r2, mul2 :: bytes
L0:
    r0 = b'foobar'
    x = r0
    r1 = b'foobarfoobar'
    mul = r1
    r2 = b'foobarfoobar'
    mul2 = r2
    return 1

[case testComplexConstantFolding]
from typing_extensions import Final

N: Final = 1
FLOAT_N: Final = 1.5

def integral() -> None:
    pos = 1+2j
    pos_2 = 2j+N
    neg = 1-2j
    neg_2 = 2j-N
def floating() -> None:
    pos = 1.5+2j
    pos_2 = 2j+FLOAT_N
    neg = 1.5-2j
    neg_2 = 2j-FLOAT_N
[out]
def integral():
    r0, pos, r1, pos_2, r2, neg, r3, neg_2 :: object
L0:
    r0 = (1+2j)
    pos = r0
    r1 = (1+2j)
    pos_2 = r1
    r2 = (1-2j)
    neg = r2
    r3 = (-1+2j)
    neg_2 = r3
    return 1
def floating():
    r0, pos, r1, pos_2, r2, neg, r3, neg_2 :: object
L0:
    r0 = (1.5+2j)
    pos = r0
    r1 = (1.5+2j)
    pos_2 = r1
    r2 = (1.5-2j)
    neg = r2
    r3 = (-1.5+2j)
    neg_2 = r3
    return 1

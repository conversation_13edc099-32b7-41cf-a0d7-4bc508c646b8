[case testListGet]
from typing import List
def f(x: List[int]) -> int:
    return x[0]
[out]
def f(x):
    x :: list
    r0 :: object
    r1 :: int
L0:
    r0 = CPyList_GetItemShort(x, 0)
    r1 = unbox(int, r0)
    return r1

[case testListOfListGet]
from typing import List
def f(x: List[List[int]]) -> List[int]:
    return x[0]
[out]
def f(x):
    x :: list
    r0 :: object
    r1 :: list
L0:
    r0 = CPyList_GetItemShort(x, 0)
    r1 = cast(list, r0)
    return r1

[case testListOfListGet2]
from typing import List
def f(x: List[List[int]]) -> int:
    return x[0][1]
[out]
def f(x):
    x :: list
    r0 :: object
    r1 :: list
    r2 :: object
    r3 :: int
L0:
    r0 = CPyList_GetItemShortBorrow(x, 0)
    r1 = borrow cast(list, r0)
    r2 = CPyList_GetItemShort(r1, 2)
    r3 = unbox(int, r2)
    keep_alive x, r0
    return r3

[case testListSet]
from typing import List
def f(x: List[int]) -> None:
    x[0] = 1
[out]
def f(x):
    x :: list
    r0 :: object
    r1 :: bit
L0:
    r0 = object 1
    r1 = CPyList_SetItem(x, 0, r0)
    return 1

[case testNewListEmpty]
from typing import List
def f() -> None:
    x = []  # type: List[int]
[out]
def f():
    r0, x :: list
L0:
    r0 = PyList_New(0)
    x = r0
    return 1

[case testNewListEmptyViaFunc]
from typing import List
def f() -> None:
    x: List[int] = list()

[out]
def f():
    r0, x :: list
L0:
    r0 = PyList_New(0)
    x = r0
    return 1

[case testNewListEmptyViaAlias]
from typing import List

ListAlias = list

def f() -> None:
    x: List[int] = ListAlias()

[out]
def f():
    r0, x :: list
L0:
    r0 = PyList_New(0)
    x = r0
    return 1

[case testNewListTwoItems]
from typing import List
def f() -> None:
    x: List[int] = [1, 2]
[out]
def f():
    r0 :: list
    r1, r2 :: object
    r3, r4, r5 :: ptr
    x :: list
L0:
    r0 = PyList_New(2)
    r1 = object 1
    r2 = object 2
    r3 = get_element_ptr r0 ob_item :: PyListObject
    r4 = load_mem r3 :: ptr*
    set_mem r4, r1 :: builtins.object*
    r5 = r4 + WORD_SIZE*1
    set_mem r5, r2 :: builtins.object*
    keep_alive r0
    x = r0
    return 1

[case testNewListTenItems]
from typing import List
def f() -> None:
    x: List[str] = ['a', 'b', 'c', 'd', 'e',
                    'f', 'g', 'h', 'i', 'j']
[out]
def f():
    r0, r1, r2, r3, r4, r5, r6, r7, r8, r9 :: str
    r10, x :: list
L0:
    r0 = 'a'
    r1 = 'b'
    r2 = 'c'
    r3 = 'd'
    r4 = 'e'
    r5 = 'f'
    r6 = 'g'
    r7 = 'h'
    r8 = 'i'
    r9 = 'j'
    r10 = CPyList_Build(10, r0, r1, r2, r3, r4, r5, r6, r7, r8, r9)
    x = r10
    return 1

[case testListMultiply]
from typing import List
def f(a: List[int]) -> None:
    b = a * 2
    b = 3 * [4]
[out]
def f(a):
    a, r0, b, r1 :: list
    r2 :: object
    r3, r4 :: ptr
    r5 :: list
L0:
    r0 = CPySequence_Multiply(a, 4)
    b = r0
    r1 = PyList_New(1)
    r2 = object 4
    r3 = get_element_ptr r1 ob_item :: PyListObject
    r4 = load_mem r3 :: ptr*
    set_mem r4, r2 :: builtins.object*
    keep_alive r1
    r5 = CPySequence_RMultiply(6, r1)
    b = r5
    return 1

[case testListLen]
from typing import List
def f(a: List[int]) -> int:
    return len(a)
[out]
def f(a):
    a :: list
    r0 :: ptr
    r1 :: native_int
    r2 :: short_int
L0:
    r0 = get_element_ptr a ob_size :: PyVarObject
    r1 = load_mem r0 :: native_int*
    keep_alive a
    r2 = r1 << 1
    return r2

[case testListAppend]
from typing import List
def f(a: List[int], x: int) -> None:
    a.append(x)
[out]
def f(a, x):
    a :: list
    x :: int
    r0 :: object
    r1 :: i32
    r2 :: bit
L0:
    r0 = box(int, x)
    r1 = PyList_Append(a, r0)
    r2 = r1 >= 0 :: signed
    return 1

[case testIndexLvalue]
from typing import List
def increment(l: List[int]) -> List[int]:
    for i in range(len(l)):
        l[i] += 1
    return l
[out]
def increment(l):
    l :: list
    r0 :: ptr
    r1 :: native_int
    r2, r3 :: short_int
    i :: int
    r4 :: bit
    r5, r6, r7 :: object
    r8 :: bit
    r9 :: short_int
L0:
    r0 = get_element_ptr l ob_size :: PyVarObject
    r1 = load_mem r0 :: native_int*
    keep_alive l
    r2 = r1 << 1
    r3 = 0
    i = r3
L1:
    r4 = r3 < r2 :: signed
    if r4 goto L2 else goto L4 :: bool
L2:
    r5 = CPyList_GetItem(l, i)
    r6 = object 1
    r7 = PyNumber_InPlaceAdd(r5, r6)
    r8 = CPyList_SetItem(l, i, r7)
L3:
    r9 = r3 + 2
    r3 = r9
    i = r9
    goto L1
L4:
    return l

[case testListDisplay]
from typing import List
def f(x: List[int], y: List[int]) -> List[int]:
    return [1, 2, *x, *y, 3]
[out]
def f(x, y):
    x, y, r0 :: list
    r1, r2 :: object
    r3, r4, r5 :: ptr
    r6, r7, r8 :: object
    r9 :: i32
    r10 :: bit
L0:
    r0 = PyList_New(2)
    r1 = object 1
    r2 = object 2
    r3 = get_element_ptr r0 ob_item :: PyListObject
    r4 = load_mem r3 :: ptr*
    set_mem r4, r1 :: builtins.object*
    r5 = r4 + WORD_SIZE*1
    set_mem r5, r2 :: builtins.object*
    keep_alive r0
    r6 = CPyList_Extend(r0, x)
    r7 = CPyList_Extend(r0, y)
    r8 = object 3
    r9 = PyList_Append(r0, r8)
    r10 = r9 >= 0 :: signed
    return r0

[case testListIn]
from typing import List
def f(x: List[int], y: int) -> bool:
    return y in x
[out]
def f(x, y):
    x :: list
    y :: int
    r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
L0:
    r0 = box(int, y)
    r1 = PySequence_Contains(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    return r3

[case testListInsert]
from typing import List
def f(x: List[int], y: int) -> None:
    x.insert(0, y)
[out]
def f(x, y):
    x :: list
    y :: int
    r0 :: object
    r1 :: i32
    r2 :: bit
L0:
    r0 = box(int, y)
    r1 = CPyList_Insert(x, 0, r0)
    r2 = r1 >= 0 :: signed
    return 1

[case testListBuiltFromGenerator]
from typing import List
def f(source: List[int]) -> None:
    a = list(x + 1 for x in source)
    b = [x + 1 for x in source]
[out]
def f(source):
    source :: list
    r0 :: ptr
    r1 :: native_int
    r2 :: list
    r3 :: short_int
    r4 :: ptr
    r5 :: native_int
    r6 :: short_int
    r7 :: bit
    r8 :: object
    r9, x, r10 :: int
    r11 :: object
    r12 :: bit
    r13 :: short_int
    a :: list
    r14 :: ptr
    r15 :: native_int
    r16 :: list
    r17 :: short_int
    r18 :: ptr
    r19 :: native_int
    r20 :: short_int
    r21 :: bit
    r22 :: object
    r23, x_2, r24 :: int
    r25 :: object
    r26 :: bit
    r27 :: short_int
    b :: list
L0:
    r0 = get_element_ptr source ob_size :: PyVarObject
    r1 = load_mem r0 :: native_int*
    keep_alive source
    r2 = PyList_New(r1)
    r3 = 0
L1:
    r4 = get_element_ptr source ob_size :: PyVarObject
    r5 = load_mem r4 :: native_int*
    keep_alive source
    r6 = r5 << 1
    r7 = r3 < r6 :: signed
    if r7 goto L2 else goto L4 :: bool
L2:
    r8 = CPyList_GetItemUnsafe(source, r3)
    r9 = unbox(int, r8)
    x = r9
    r10 = CPyTagged_Add(x, 2)
    r11 = box(int, r10)
    r12 = CPyList_SetItemUnsafe(r2, r3, r11)
L3:
    r13 = r3 + 2
    r3 = r13
    goto L1
L4:
    a = r2
    r14 = get_element_ptr source ob_size :: PyVarObject
    r15 = load_mem r14 :: native_int*
    keep_alive source
    r16 = PyList_New(r15)
    r17 = 0
L5:
    r18 = get_element_ptr source ob_size :: PyVarObject
    r19 = load_mem r18 :: native_int*
    keep_alive source
    r20 = r19 << 1
    r21 = r17 < r20 :: signed
    if r21 goto L6 else goto L8 :: bool
L6:
    r22 = CPyList_GetItemUnsafe(source, r17)
    r23 = unbox(int, r22)
    x_2 = r23
    r24 = CPyTagged_Add(x_2, 2)
    r25 = box(int, r24)
    r26 = CPyList_SetItemUnsafe(r16, r17, r25)
L7:
    r27 = r17 + 2
    r17 = r27
    goto L5
L8:
    b = r16
    return 1
[case testGeneratorNext]
from typing import List, Optional

def test(x: List[int]) -> None:
    res = next((i for i in x), None)
[out]
def test(x):
    x :: list
    r0 :: short_int
    r1 :: ptr
    r2 :: native_int
    r3 :: short_int
    r4 :: bit
    r5 :: object
    r6, i :: int
    r7 :: object
    r8 :: union[int, None]
    r9 :: short_int
    r10 :: object
    res :: union[int, None]
L0:
    r0 = 0
L1:
    r1 = get_element_ptr x ob_size :: PyVarObject
    r2 = load_mem r1 :: native_int*
    keep_alive x
    r3 = r2 << 1
    r4 = r0 < r3 :: signed
    if r4 goto L2 else goto L4 :: bool
L2:
    r5 = CPyList_GetItemUnsafe(x, r0)
    r6 = unbox(int, r5)
    i = r6
    r7 = box(int, i)
    r8 = r7
    goto L5
L3:
    r9 = r0 + 2
    r0 = r9
    goto L1
L4:
    r10 = box(None, 1)
    r8 = r10
L5:
    res = r8
    return 1

[case testSimplifyListUnion]
from typing import List, Union, Optional

def narrow(a: Union[List[str], List[bytes], int]) -> int:
    if isinstance(a, list):
        return len(a)
    return a
def loop(a: Union[List[str], List[bytes]]) -> None:
    for x in a:
         pass
def nested_union(a: Union[List[str], List[Optional[str]]]) -> None:
    for x in a:
        pass
[out]
def narrow(a):
    a :: union[list, int]
    r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: list
    r5 :: ptr
    r6 :: native_int
    r7 :: short_int
    r8 :: int
L0:
    r0 = load_address PyList_Type
    r1 = PyObject_IsInstance(a, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L2 :: bool
L1:
    r4 = borrow cast(list, a)
    r5 = get_element_ptr r4 ob_size :: PyVarObject
    r6 = load_mem r5 :: native_int*
    keep_alive r4
    r7 = r6 << 1
    keep_alive a
    return r7
L2:
    r8 = unbox(int, a)
    return r8
def loop(a):
    a :: list
    r0 :: short_int
    r1 :: ptr
    r2 :: native_int
    r3 :: short_int
    r4 :: bit
    r5 :: object
    r6, x :: union[str, bytes]
    r7 :: short_int
L0:
    r0 = 0
L1:
    r1 = get_element_ptr a ob_size :: PyVarObject
    r2 = load_mem r1 :: native_int*
    keep_alive a
    r3 = r2 << 1
    r4 = r0 < r3 :: signed
    if r4 goto L2 else goto L4 :: bool
L2:
    r5 = CPyList_GetItemUnsafe(a, r0)
    r6 = cast(union[str, bytes], r5)
    x = r6
L3:
    r7 = r0 + 2
    r0 = r7
    goto L1
L4:
    return 1
def nested_union(a):
    a :: list
    r0 :: short_int
    r1 :: ptr
    r2 :: native_int
    r3 :: short_int
    r4 :: bit
    r5 :: object
    r6, x :: union[str, None]
    r7 :: short_int
L0:
    r0 = 0
L1:
    r1 = get_element_ptr a ob_size :: PyVarObject
    r2 = load_mem r1 :: native_int*
    keep_alive a
    r3 = r2 << 1
    r4 = r0 < r3 :: signed
    if r4 goto L2 else goto L4 :: bool
L2:
    r5 = CPyList_GetItemUnsafe(a, r0)
    r6 = cast(union[str, None], r5)
    x = r6
L3:
    r7 = r0 + 2
    r0 = r7
    goto L1
L4:
    return 1

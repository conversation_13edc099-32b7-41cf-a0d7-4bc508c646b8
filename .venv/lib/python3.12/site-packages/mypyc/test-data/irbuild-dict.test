[case testDictGet]
from typing import Dict
def f(d: Dict[int, bool]) -> bool:
    return d[0]
[out]
def f(d):
    d :: dict
    r0, r1 :: object
    r2 :: bool
L0:
    r0 = object 0
    r1 = CPyDict_GetItem(d, r0)
    r2 = unbox(bool, r1)
    return r2

[case testDictSet]
from typing import Dict
def f(d: Dict[int, bool]) -> None:
    d[0] = False
[out]
def f(d):
    d :: dict
    r0, r1 :: object
    r2 :: i32
    r3 :: bit
L0:
    r0 = object 0
    r1 = box(bool, 0)
    r2 = CPyDict_SetItem(d, r0, r1)
    r3 = r2 >= 0 :: signed
    return 1

[case testNewEmptyDict]
from typing import Dict
def f() -> None:
    d = {}  # type: Dict[bool, int]
[out]
def f():
    r0, d :: dict
L0:
    r0 = PyDict_New()
    d = r0
    return 1

[case testNewEmptyDictViaFunc]
from typing import Dict
def f() -> None:
    d: Dict[bool, int] = dict()

[out]
def f():
    r0, d :: dict
L0:
    r0 = PyDict_New()
    d = r0
    return 1

[case testNewDictWithValues]
def f(x: object) -> None:
    d = {1: 2, '': x}
[out]
def f(x):
    x :: object
    r0 :: str
    r1, r2 :: object
    r3, d :: dict
L0:
    r0 = ''
    r1 = object 1
    r2 = object 2
    r3 = CPyDict_Build(2, r1, r2, r0, x)
    d = r3
    return 1

[case testInDict]
from typing import Dict
def f(d: Dict[int, int]) -> bool:
    if 4 in d:
        return True
    else:
        return False
[out]
def f(d):
    d :: dict
    r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
L0:
    r0 = object 4
    r1 = PyDict_Contains(d, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L2 :: bool
L1:
    return 1
L2:
    return 0
L3:
    unreachable

[case testNotInDict]
from typing import Dict
def f(d: Dict[int, int]) -> bool:
    if 4 not in d:
        return True
    else:
        return False
[out]
def f(d):
    d :: dict
    r0 :: object
    r1 :: i32
    r2 :: bit
    r3, r4 :: bool
L0:
    r0 = object 4
    r1 = PyDict_Contains(d, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    r4 = r3 ^ 1
    if r4 goto L1 else goto L2 :: bool
L1:
    return 1
L2:
    return 0
L3:
    unreachable

[case testDictUpdate]
from typing import Dict
def f(a: Dict[int, int], b: Dict[int, int]) -> None:
    a.update(b)
[out]
def f(a, b):
    a, b :: dict
    r0 :: i32
    r1 :: bit
L0:
    r0 = CPyDict_Update(a, b)
    r1 = r0 >= 0 :: signed
    return 1

[case testDictKeyLvalue]
from typing import Dict
def increment(d: Dict[str, int]) -> Dict[str, int]:
    for k in d:
        d[k] += 1
    return d
[out]
def increment(d):
    d :: dict
    r0 :: short_int
    r1 :: native_int
    r2 :: short_int
    r3 :: object
    r4 :: tuple[bool, short_int, object]
    r5 :: short_int
    r6 :: bool
    r7 :: object
    r8, k :: str
    r9, r10, r11 :: object
    r12 :: i32
    r13, r14, r15 :: bit
L0:
    r0 = 0
    r1 = PyDict_Size(d)
    r2 = r1 << 1
    r3 = CPyDict_GetKeysIter(d)
L1:
    r4 = CPyDict_NextKey(r3, r0)
    r5 = r4[1]
    r0 = r5
    r6 = r4[0]
    if r6 goto L2 else goto L4 :: bool
L2:
    r7 = r4[2]
    r8 = cast(str, r7)
    k = r8
    r9 = CPyDict_GetItem(d, k)
    r10 = object 1
    r11 = PyNumber_InPlaceAdd(r9, r10)
    r12 = CPyDict_SetItem(d, k, r11)
    r13 = r12 >= 0 :: signed
L3:
    r14 = CPyDict_CheckSize(d, r2)
    goto L1
L4:
    r15 = CPy_NoErrOccured()
L5:
    return d

[case testDictDisplay]
from typing import Dict
def f(x: str, y: Dict[str, int]) -> Dict[str, int]:
    return {x: 2, **y, 'z': 3}
[out]
def f(x, y):
    x :: str
    y :: dict
    r0 :: str
    r1 :: object
    r2 :: dict
    r3 :: i32
    r4 :: bit
    r5 :: object
    r6 :: i32
    r7 :: bit
L0:
    r0 = 'z'
    r1 = object 2
    r2 = CPyDict_Build(1, x, r1)
    r3 = CPyDict_UpdateInDisplay(r2, y)
    r4 = r3 >= 0 :: signed
    r5 = object 3
    r6 = CPyDict_SetItem(r2, r0, r5)
    r7 = r6 >= 0 :: signed
    return r2

[case testDictIterationMethods]
from typing import Dict, Union
from typing_extensions import TypedDict

class Person(TypedDict):
    name: str
    age: int

def print_dict_methods(d1: Dict[int, int], d2: Dict[int, int]) -> None:
    for v in d1.values():
        if v in d2:
            return
    for k, v in d2.items():
        d2[k] += v
def union_of_dicts(d: Union[Dict[str, int], Dict[str, str]]) -> None:
    new = {}
    for k, v in d.items():
        new[k] = int(v)
def typeddict(d: Person) -> None:
    for k, v in d.items():
        if k == "name":
            name = v
[out]
def print_dict_methods(d1, d2):
    d1, d2 :: dict
    r0 :: short_int
    r1 :: native_int
    r2 :: short_int
    r3 :: object
    r4 :: tuple[bool, short_int, object]
    r5 :: short_int
    r6 :: bool
    r7 :: object
    r8, v :: int
    r9 :: object
    r10 :: i32
    r11 :: bit
    r12 :: bool
    r13, r14 :: bit
    r15 :: short_int
    r16 :: native_int
    r17 :: short_int
    r18 :: object
    r19 :: tuple[bool, short_int, object, object]
    r20 :: short_int
    r21 :: bool
    r22, r23 :: object
    r24, r25, k :: int
    r26, r27, r28, r29, r30 :: object
    r31 :: i32
    r32, r33, r34 :: bit
L0:
    r0 = 0
    r1 = PyDict_Size(d1)
    r2 = r1 << 1
    r3 = CPyDict_GetValuesIter(d1)
L1:
    r4 = CPyDict_NextValue(r3, r0)
    r5 = r4[1]
    r0 = r5
    r6 = r4[0]
    if r6 goto L2 else goto L6 :: bool
L2:
    r7 = r4[2]
    r8 = unbox(int, r7)
    v = r8
    r9 = box(int, v)
    r10 = PyDict_Contains(d2, r9)
    r11 = r10 >= 0 :: signed
    r12 = truncate r10: i32 to builtins.bool
    if r12 goto L3 else goto L4 :: bool
L3:
    return 1
L4:
L5:
    r13 = CPyDict_CheckSize(d1, r2)
    goto L1
L6:
    r14 = CPy_NoErrOccured()
L7:
    r15 = 0
    r16 = PyDict_Size(d2)
    r17 = r16 << 1
    r18 = CPyDict_GetItemsIter(d2)
L8:
    r19 = CPyDict_NextItem(r18, r15)
    r20 = r19[1]
    r15 = r20
    r21 = r19[0]
    if r21 goto L9 else goto L11 :: bool
L9:
    r22 = r19[2]
    r23 = r19[3]
    r24 = unbox(int, r22)
    r25 = unbox(int, r23)
    k = r24
    v = r25
    r26 = box(int, k)
    r27 = CPyDict_GetItem(d2, r26)
    r28 = box(int, v)
    r29 = PyNumber_InPlaceAdd(r27, r28)
    r30 = box(int, k)
    r31 = CPyDict_SetItem(d2, r30, r29)
    r32 = r31 >= 0 :: signed
L10:
    r33 = CPyDict_CheckSize(d2, r17)
    goto L8
L11:
    r34 = CPy_NoErrOccured()
L12:
    return 1
def union_of_dicts(d):
    d, r0, new :: dict
    r1 :: short_int
    r2 :: native_int
    r3 :: short_int
    r4 :: object
    r5 :: tuple[bool, short_int, object, object]
    r6 :: short_int
    r7 :: bool
    r8, r9 :: object
    r10 :: str
    r11 :: union[int, str]
    k :: str
    v :: union[int, str]
    r12, r13 :: object
    r14 :: int
    r15 :: object
    r16 :: i32
    r17, r18, r19 :: bit
L0:
    r0 = PyDict_New()
    new = r0
    r1 = 0
    r2 = PyDict_Size(d)
    r3 = r2 << 1
    r4 = CPyDict_GetItemsIter(d)
L1:
    r5 = CPyDict_NextItem(r4, r1)
    r6 = r5[1]
    r1 = r6
    r7 = r5[0]
    if r7 goto L2 else goto L4 :: bool
L2:
    r8 = r5[2]
    r9 = r5[3]
    r10 = cast(str, r8)
    r11 = cast(union[int, str], r9)
    k = r10
    v = r11
    r12 = load_address PyLong_Type
    r13 = PyObject_CallFunctionObjArgs(r12, v, 0)
    r14 = unbox(int, r13)
    r15 = box(int, r14)
    r16 = CPyDict_SetItem(new, k, r15)
    r17 = r16 >= 0 :: signed
L3:
    r18 = CPyDict_CheckSize(d, r3)
    goto L1
L4:
    r19 = CPy_NoErrOccured()
L5:
    return 1
def typeddict(d):
    d :: dict
    r0 :: short_int
    r1 :: native_int
    r2 :: short_int
    r3 :: object
    r4 :: tuple[bool, short_int, object, object]
    r5 :: short_int
    r6 :: bool
    r7, r8 :: object
    r9, k :: str
    v :: object
    r10 :: str
    r11 :: i32
    r12 :: bit
    r13 :: object
    r14, r15, r16 :: bit
    name :: object
    r17, r18 :: bit
L0:
    r0 = 0
    r1 = PyDict_Size(d)
    r2 = r1 << 1
    r3 = CPyDict_GetItemsIter(d)
L1:
    r4 = CPyDict_NextItem(r3, r0)
    r5 = r4[1]
    r0 = r5
    r6 = r4[0]
    if r6 goto L2 else goto L9 :: bool
L2:
    r7 = r4[2]
    r8 = r4[3]
    r9 = cast(str, r7)
    k = r9
    v = r8
    r10 = 'name'
    r11 = PyUnicode_Compare(k, r10)
    r12 = r11 == -1
    if r12 goto L3 else goto L5 :: bool
L3:
    r13 = PyErr_Occurred()
    r14 = r13 != 0
    if r14 goto L4 else goto L5 :: bool
L4:
    r15 = CPy_KeepPropagating()
L5:
    r16 = r11 == 0
    if r16 goto L6 else goto L7 :: bool
L6:
    name = v
L7:
L8:
    r17 = CPyDict_CheckSize(d, r2)
    goto L1
L9:
    r18 = CPy_NoErrOccured()
L10:
    return 1

[case testDictLoadAddress]
def f() -> None:
    x = dict
[out]
def f():
    r0, x :: object
L0:
    r0 = load_address PyDict_Type
    x = r0
    return 1

[case testDictClear]
from typing import Dict
def f(d: Dict[int, int]) -> None:
    return d.clear()
[out]
def f(d):
    d :: dict
    r0 :: bit
L0:
    r0 = CPyDict_Clear(d)
    return 1

[case testDictCopy]
from typing import Dict
def f(d: Dict[int, int]) -> Dict[int, int]:
    return d.copy()
[out]
def f(d):
    d, r0 :: dict
L0:
    r0 = CPyDict_Copy(d)
    return r0

[case testDictSetdefault]
from typing import Dict
def f(d: Dict[object, object]) -> object:
    return d.setdefault('a', 'b')

def f2(d: Dict[object, object], flag: bool) -> object:
    if flag:
        return d.setdefault('a', set())
    else:
        return d.setdefault('a', set('b'))

def f3(d: Dict[object, object], flag: bool) -> object:
    if flag:
        return d.setdefault('a', [])
    else:
        return d.setdefault('a', [1])

def f4(d: Dict[object, object], flag: bool) -> object:
    if flag:
        return d.setdefault('a', {})
    else:
        return d.setdefault('a', {'c': 1})
[out]
def f(d):
    d :: dict
    r0, r1 :: str
    r2 :: object
L0:
    r0 = 'a'
    r1 = 'b'
    r2 = CPyDict_SetDefault(d, r0, r1)
    return r2
def f2(d, flag):
    d :: dict
    flag :: bool
    r0 :: str
    r1 :: object
    r2, r3 :: str
    r4 :: set
    r5, r6 :: object
L0:
    if flag goto L1 else goto L2 :: bool
L1:
    r0 = 'a'
    r1 = CPyDict_SetDefaultWithEmptyDatatype(d, r0, 3)
    return r1
L2:
    r2 = 'a'
    r3 = 'b'
    r4 = PySet_New(r3)
    r5 = CPyDict_SetDefault(d, r2, r4)
    return r5
L3:
    r6 = box(None, 1)
    return r6
def f3(d, flag):
    d :: dict
    flag :: bool
    r0 :: str
    r1 :: object
    r2 :: str
    r3 :: list
    r4 :: object
    r5, r6 :: ptr
    r7, r8 :: object
L0:
    if flag goto L1 else goto L2 :: bool
L1:
    r0 = 'a'
    r1 = CPyDict_SetDefaultWithEmptyDatatype(d, r0, 1)
    return r1
L2:
    r2 = 'a'
    r3 = PyList_New(1)
    r4 = object 1
    r5 = get_element_ptr r3 ob_item :: PyListObject
    r6 = load_mem r5 :: ptr*
    set_mem r6, r4 :: builtins.object*
    keep_alive r3
    r7 = CPyDict_SetDefault(d, r2, r3)
    return r7
L3:
    r8 = box(None, 1)
    return r8
def f4(d, flag):
    d :: dict
    flag :: bool
    r0 :: str
    r1 :: object
    r2, r3 :: str
    r4 :: object
    r5 :: dict
    r6, r7 :: object
L0:
    if flag goto L1 else goto L2 :: bool
L1:
    r0 = 'a'
    r1 = CPyDict_SetDefaultWithEmptyDatatype(d, r0, 2)
    return r1
L2:
    r2 = 'a'
    r3 = 'c'
    r4 = object 1
    r5 = CPyDict_Build(1, r3, r4)
    r6 = CPyDict_SetDefault(d, r2, r5)
    return r6
L3:
    r7 = box(None, 1)
    return r7

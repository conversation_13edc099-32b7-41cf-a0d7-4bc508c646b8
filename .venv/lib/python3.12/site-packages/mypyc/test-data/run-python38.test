-- Test cases for Python 3.8 features

[case testWalrus1]
from typing import Optional

def foo(x: int) -> Optional[int]:
    if x < 0:
        return None
    return x

def test(x: int) -> str:
    if (n := foo(x)) is not None:
        return str(x)
    else:
        return "<fail>"

[file driver.py]
from native import test

assert test(10) == "10"
assert test(-1) == "<fail>"


[case testWalrus2]
from typing import Optional, Tuple, List

class Node:
    def __init__(self, val: int, next: Optional['Node']) -> None:
        self.val = val
        self.next = next

def pairs(nobe: Optional[Node]) -> List[Tuple[int, int]]:
    if nobe is None:
        return []
    l = []
    while next := nobe.next:
        l.append((nobe.val, next.val))
        nobe = next
    return l

def make(l: List[int]) -> Optional[Node]:
    cur: Optional[Node] = None
    for x in reversed(l):
        cur = Node(x, cur)
    return cur

[file driver.py]
from native import Node, make, pairs

assert pairs(make([1,2,3])) == [(1,2), (2,3)]
assert pairs(make([1])) == []
assert pairs(make([])) == []

[case testFStrings]
from datetime import datetime

def test_fstring_equal_sign() -> None:
    today = datetime(year=2017, month=1, day=27)
    assert f"{today=:%B %d, %Y}" == 'today=January 27, 2017' # using date format specifier and debugging

    foo = "bar"
    assert f"{ foo = }" == " foo = 'bar'" # preserves whitespace

    line = "The mill's closed"
    assert f"{line = }" == 'line = "The mill\'s closed"'
    assert f"{line = :20}" == "line = The mill's closed   "
    assert f"{line = !r:20}" == 'line = "The mill\'s closed" '

[case testMethodOverrideDefaultPosOnly1]
class Foo:
    def f(self, x: int=20, /, *, z: int=10) -> None:
        pass

class Bar(Foo):
    def f(self, *args: int, **kwargs: int) -> None:
        print("stuff", args, kwargs)

z: Foo = Bar()
z.f(1, z=50)
z.f()
z.f(1)
z.f(z=50)

[out]
stuff (1,) {'z': 50}
stuff () {}
stuff (1,) {}
stuff () {'z': 50}

[case testMatchValuePattern_python3_10]
def f():
    match 123:
        case 123:
            print("matched")
[out]
def f():
    r0 :: bit
    r1 :: str
    r2 :: object
    r3 :: str
    r4 :: object
    r5 :: object[1]
    r6 :: object_ptr
    r7, r8 :: object
L0:
    r0 = 246 == 246
    if r0 goto L1 else goto L2 :: bool
L1:
    r1 = 'matched'
    r2 = builtins :: module
    r3 = 'print'
    r4 = CPyObject_GetAttr(r2, r3)
    r5 = [r1]
    r6 = load_address r5
    r7 = _PyObject_Vectorcall(r4, r6, 1, 0)
    keep_alive r1
    goto L3
L2:
L3:
    r8 = box(None, 1)
    return r8
[case testMatchOrPattern_python3_10]
def f():
    match 123:
        case 123 | 456:
            print("matched")
[out]
def f():
    r0, r1 :: bit
    r2 :: str
    r3 :: object
    r4 :: str
    r5 :: object
    r6 :: object[1]
    r7 :: object_ptr
    r8, r9 :: object
L0:
    r0 = 246 == 246
    if r0 goto L3 else goto L1 :: bool
L1:
    r1 = 246 == 912
    if r1 goto L3 else goto L2 :: bool
L2:
    goto L4
L3:
    r2 = 'matched'
    r3 = builtins :: module
    r4 = 'print'
    r5 = CPyObject_GetAttr(r3, r4)
    r6 = [r2]
    r7 = load_address r6
    r8 = _PyObject_Vectorcall(r5, r7, 1, 0)
    keep_alive r2
    goto L5
L4:
L5:
    r9 = box(None, 1)
    return r9
[case testMatchOrPatternManyPatterns_python3_10]
def f():
    match 1:
        case 1 | 2 | 3 | 4:
            print("matched")
[out]
def f():
    r0, r1, r2, r3 :: bit
    r4 :: str
    r5 :: object
    r6 :: str
    r7 :: object
    r8 :: object[1]
    r9 :: object_ptr
    r10, r11 :: object
L0:
    r0 = 2 == 2
    if r0 goto L5 else goto L1 :: bool
L1:
    r1 = 2 == 4
    if r1 goto L5 else goto L2 :: bool
L2:
    r2 = 2 == 6
    if r2 goto L5 else goto L3 :: bool
L3:
    r3 = 2 == 8
    if r3 goto L5 else goto L4 :: bool
L4:
    goto L6
L5:
    r4 = 'matched'
    r5 = builtins :: module
    r6 = 'print'
    r7 = CPyObject_GetAttr(r5, r6)
    r8 = [r4]
    r9 = load_address r8
    r10 = _PyObject_Vectorcall(r7, r9, 1, 0)
    keep_alive r4
    goto L7
L6:
L7:
    r11 = box(None, 1)
    return r11
[case testMatchClassPattern_python3_10]
def f():
    match 123:
        case int():
            print("matched")
[out]
def f():
    r0, r1 :: object
    r2 :: bool
    r3 :: str
    r4 :: object
    r5 :: str
    r6 :: object
    r7 :: object[1]
    r8 :: object_ptr
    r9, r10 :: object
L0:
    r0 = load_address PyLong_Type
    r1 = object 123
    r2 = CPy_TypeCheck(r1, r0)
    if r2 goto L1 else goto L2 :: bool
L1:
    r3 = 'matched'
    r4 = builtins :: module
    r5 = 'print'
    r6 = CPyObject_GetAttr(r4, r5)
    r7 = [r3]
    r8 = load_address r7
    r9 = _PyObject_Vectorcall(r6, r8, 1, 0)
    keep_alive r3
    goto L3
L2:
L3:
    r10 = box(None, 1)
    return r10
[case testMatchExaustivePattern_python3_10]
def f():
    match 123:
        case _:
            print("matched")
[out]
def f():
    r0 :: str
    r1 :: object
    r2 :: str
    r3 :: object
    r4 :: object[1]
    r5 :: object_ptr
    r6, r7 :: object
L0:
L1:
    r0 = 'matched'
    r1 = builtins :: module
    r2 = 'print'
    r3 = CPyObject_GetAttr(r1, r2)
    r4 = [r0]
    r5 = load_address r4
    r6 = _PyObject_Vectorcall(r3, r5, 1, 0)
    keep_alive r0
    goto L3
L2:
L3:
    r7 = box(None, 1)
    return r7
[case testMatchMultipleBodies_python3_10]
def f():
    match 123:
        case 123:
            print("matched")
        case 456:
            print("no match")
[out]
def f():
    r0 :: bit
    r1 :: str
    r2 :: object
    r3 :: str
    r4 :: object
    r5 :: object[1]
    r6 :: object_ptr
    r7 :: object
    r8 :: bit
    r9 :: str
    r10 :: object
    r11 :: str
    r12 :: object
    r13 :: object[1]
    r14 :: object_ptr
    r15, r16 :: object
L0:
    r0 = 246 == 246
    if r0 goto L1 else goto L2 :: bool
L1:
    r1 = 'matched'
    r2 = builtins :: module
    r3 = 'print'
    r4 = CPyObject_GetAttr(r2, r3)
    r5 = [r1]
    r6 = load_address r5
    r7 = _PyObject_Vectorcall(r4, r6, 1, 0)
    keep_alive r1
    goto L5
L2:
    r8 = 246 == 912
    if r8 goto L3 else goto L4 :: bool
L3:
    r9 = 'no match'
    r10 = builtins :: module
    r11 = 'print'
    r12 = CPyObject_GetAttr(r10, r11)
    r13 = [r9]
    r14 = load_address r13
    r15 = _PyObject_Vectorcall(r12, r14, 1, 0)
    keep_alive r9
    goto L5
L4:
L5:
    r16 = box(None, 1)
    return r16
[case testMatchMultiBodyAndComplexOr_python3_10]
def f():
    match 123:
        case 1:
            print("here 1")
        case 2 | 3:
            print("here 2 | 3")
        case 123:
            print("here 123")
[out]
def f():
    r0 :: bit
    r1 :: str
    r2 :: object
    r3 :: str
    r4 :: object
    r5 :: object[1]
    r6 :: object_ptr
    r7 :: object
    r8, r9 :: bit
    r10 :: str
    r11 :: object
    r12 :: str
    r13 :: object
    r14 :: object[1]
    r15 :: object_ptr
    r16 :: object
    r17 :: bit
    r18 :: str
    r19 :: object
    r20 :: str
    r21 :: object
    r22 :: object[1]
    r23 :: object_ptr
    r24, r25 :: object
L0:
    r0 = 246 == 2
    if r0 goto L1 else goto L2 :: bool
L1:
    r1 = 'here 1'
    r2 = builtins :: module
    r3 = 'print'
    r4 = CPyObject_GetAttr(r2, r3)
    r5 = [r1]
    r6 = load_address r5
    r7 = _PyObject_Vectorcall(r4, r6, 1, 0)
    keep_alive r1
    goto L9
L2:
    r8 = 246 == 4
    if r8 goto L5 else goto L3 :: bool
L3:
    r9 = 246 == 6
    if r9 goto L5 else goto L4 :: bool
L4:
    goto L6
L5:
    r10 = 'here 2 | 3'
    r11 = builtins :: module
    r12 = 'print'
    r13 = CPyObject_GetAttr(r11, r12)
    r14 = [r10]
    r15 = load_address r14
    r16 = _PyObject_Vectorcall(r13, r15, 1, 0)
    keep_alive r10
    goto L9
L6:
    r17 = 246 == 246
    if r17 goto L7 else goto L8 :: bool
L7:
    r18 = 'here 123'
    r19 = builtins :: module
    r20 = 'print'
    r21 = CPyObject_GetAttr(r19, r20)
    r22 = [r18]
    r23 = load_address r22
    r24 = _PyObject_Vectorcall(r21, r23, 1, 0)
    keep_alive r18
    goto L9
L8:
L9:
    r25 = box(None, 1)
    return r25
[case testMatchWithGuard_python3_10]
def f():
    match 123:
        case 123 if True:
            print("matched")
[out]
def f():
    r0 :: bit
    r1 :: str
    r2 :: object
    r3 :: str
    r4 :: object
    r5 :: object[1]
    r6 :: object_ptr
    r7, r8 :: object
L0:
    r0 = 246 == 246
    if r0 goto L1 else goto L3 :: bool
L1:
    if 1 goto L2 else goto L3 :: bool
L2:
    r1 = 'matched'
    r2 = builtins :: module
    r3 = 'print'
    r4 = CPyObject_GetAttr(r2, r3)
    r5 = [r1]
    r6 = load_address r5
    r7 = _PyObject_Vectorcall(r4, r6, 1, 0)
    keep_alive r1
    goto L4
L3:
L4:
    r8 = box(None, 1)
    return r8
[case testMatchSingleton_python3_10]
def f():
    match 123:
        case True:
            print("value is True")
        case False:
            print("value is False")
        case None:
            print("value is None")
[out]
def f():
    r0, r1 :: object
    r2 :: bit
    r3 :: str
    r4 :: object
    r5 :: str
    r6 :: object
    r7 :: object[1]
    r8 :: object_ptr
    r9, r10, r11 :: object
    r12 :: bit
    r13 :: str
    r14 :: object
    r15 :: str
    r16 :: object
    r17 :: object[1]
    r18 :: object_ptr
    r19, r20, r21 :: object
    r22 :: bit
    r23 :: str
    r24 :: object
    r25 :: str
    r26 :: object
    r27 :: object[1]
    r28 :: object_ptr
    r29, r30 :: object
L0:
    r0 = object 123
    r1 = box(bool, 1)
    r2 = r0 == r1
    if r2 goto L1 else goto L2 :: bool
L1:
    r3 = 'value is True'
    r4 = builtins :: module
    r5 = 'print'
    r6 = CPyObject_GetAttr(r4, r5)
    r7 = [r3]
    r8 = load_address r7
    r9 = _PyObject_Vectorcall(r6, r8, 1, 0)
    keep_alive r3
    goto L7
L2:
    r10 = object 123
    r11 = box(bool, 0)
    r12 = r10 == r11
    if r12 goto L3 else goto L4 :: bool
L3:
    r13 = 'value is False'
    r14 = builtins :: module
    r15 = 'print'
    r16 = CPyObject_GetAttr(r14, r15)
    r17 = [r13]
    r18 = load_address r17
    r19 = _PyObject_Vectorcall(r16, r18, 1, 0)
    keep_alive r13
    goto L7
L4:
    r20 = load_address _Py_NoneStruct
    r21 = object 123
    r22 = r21 == r20
    if r22 goto L5 else goto L6 :: bool
L5:
    r23 = 'value is None'
    r24 = builtins :: module
    r25 = 'print'
    r26 = CPyObject_GetAttr(r24, r25)
    r27 = [r23]
    r28 = load_address r27
    r29 = _PyObject_Vectorcall(r26, r28, 1, 0)
    keep_alive r23
    goto L7
L6:
L7:
    r30 = box(None, 1)
    return r30
[case testMatchRecursiveOrPattern_python3_10]
def f():
    match 1:
        case 1 | int():
            print("matched")
[out]
def f():
    r0 :: bit
    r1, r2 :: object
    r3 :: bool
    r4 :: str
    r5 :: object
    r6 :: str
    r7 :: object
    r8 :: object[1]
    r9 :: object_ptr
    r10, r11 :: object
L0:
    r0 = 2 == 2
    if r0 goto L3 else goto L1 :: bool
L1:
    r1 = load_address PyLong_Type
    r2 = object 1
    r3 = CPy_TypeCheck(r2, r1)
    if r3 goto L3 else goto L2 :: bool
L2:
    goto L4
L3:
    r4 = 'matched'
    r5 = builtins :: module
    r6 = 'print'
    r7 = CPyObject_GetAttr(r5, r6)
    r8 = [r4]
    r9 = load_address r8
    r10 = _PyObject_Vectorcall(r7, r9, 1, 0)
    keep_alive r4
    goto L5
L4:
L5:
    r11 = box(None, 1)
    return r11
[case testMatchAsPattern_python3_10]
def f():
    match 123:
        case 123 as x:
            print(x)
[out]
def f():
    r0 :: bit
    r1, x, r2 :: object
    r3 :: str
    r4 :: object
    r5 :: object[1]
    r6 :: object_ptr
    r7, r8 :: object
L0:
    r0 = 246 == 246
    r1 = object 123
    x = r1
    if r0 goto L1 else goto L2 :: bool
L1:
    r2 = builtins :: module
    r3 = 'print'
    r4 = CPyObject_GetAttr(r2, r3)
    r5 = [x]
    r6 = load_address r5
    r7 = _PyObject_Vectorcall(r4, r6, 1, 0)
    keep_alive x
    goto L3
L2:
L3:
    r8 = box(None, 1)
    return r8
[case testMatchAsPatternOnOrPattern_python3_10]
def f():
    match 1:
        case (1 | 2) as x:
            print(x)
[out]
def f():
    r0 :: bit
    r1, x :: object
    r2 :: bit
    r3, r4 :: object
    r5 :: str
    r6 :: object
    r7 :: object[1]
    r8 :: object_ptr
    r9, r10 :: object
L0:
    r0 = 2 == 2
    r1 = object 1
    x = r1
    if r0 goto L3 else goto L1 :: bool
L1:
    r2 = 2 == 4
    r3 = object 2
    x = r3
    if r2 goto L3 else goto L2 :: bool
L2:
    goto L4
L3:
    r4 = builtins :: module
    r5 = 'print'
    r6 = CPyObject_GetAttr(r4, r5)
    r7 = [x]
    r8 = load_address r7
    r9 = _PyObject_Vectorcall(r6, r8, 1, 0)
    keep_alive x
    goto L5
L4:
L5:
    r10 = box(None, 1)
    return r10
[case testMatchAsPatternOnClassPattern_python3_10]
def f():
    match 123:
        case int() as i:
            print(i)
[out]
def f():
    r0, r1 :: object
    r2 :: bool
    i :: int
    r3 :: object
    r4 :: str
    r5, r6 :: object
    r7 :: object[1]
    r8 :: object_ptr
    r9, r10 :: object
L0:
    r0 = load_address PyLong_Type
    r1 = object 123
    r2 = CPy_TypeCheck(r1, r0)
    if r2 goto L1 else goto L3 :: bool
L1:
    i = 246
L2:
    r3 = builtins :: module
    r4 = 'print'
    r5 = CPyObject_GetAttr(r3, r4)
    r6 = box(int, i)
    r7 = [r6]
    r8 = load_address r7
    r9 = _PyObject_Vectorcall(r5, r8, 1, 0)
    keep_alive r6
    goto L4
L3:
L4:
    r10 = box(None, 1)
    return r10
[case testMatchClassPatternWithPositionalArgs_python3_10]
class Position:
    __match_args__ = ("x", "y", "z")

    x: int
    y: int
    z: int

def f(x):
    match x:
        case Position(1, 2, 3):
            print("matched")
[out]
def Position.__mypyc_defaults_setup(__mypyc_self__):
    __mypyc_self__ :: __main__.Position
    r0, r1, r2 :: str
    r3 :: tuple[str, str, str]
L0:
    r0 = 'x'
    r1 = 'y'
    r2 = 'z'
    r3 = (r0, r1, r2)
    __mypyc_self__.__match_args__ = r3
    return 1
def f(x):
    x, r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: str
    r5, r6, r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11 :: str
    r12, r13, r14 :: object
    r15 :: i32
    r16 :: bit
    r17 :: bool
    r18 :: str
    r19, r20, r21 :: object
    r22 :: i32
    r23 :: bit
    r24 :: bool
    r25 :: str
    r26 :: object
    r27 :: str
    r28 :: object
    r29 :: object[1]
    r30 :: object_ptr
    r31, r32 :: object
L0:
    r0 = __main__.Position :: type
    r1 = PyObject_IsInstance(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L5 :: bool
L1:
    r4 = 'x'
    r5 = CPyObject_GetAttr(x, r4)
    r6 = object 1
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L2 else goto L5 :: bool
L2:
    r11 = 'y'
    r12 = CPyObject_GetAttr(x, r11)
    r13 = object 2
    r14 = PyObject_RichCompare(r12, r13, 2)
    r15 = PyObject_IsTrue(r14)
    r16 = r15 >= 0 :: signed
    r17 = truncate r15: i32 to builtins.bool
    if r17 goto L3 else goto L5 :: bool
L3:
    r18 = 'z'
    r19 = CPyObject_GetAttr(x, r18)
    r20 = object 3
    r21 = PyObject_RichCompare(r19, r20, 2)
    r22 = PyObject_IsTrue(r21)
    r23 = r22 >= 0 :: signed
    r24 = truncate r22: i32 to builtins.bool
    if r24 goto L4 else goto L5 :: bool
L4:
    r25 = 'matched'
    r26 = builtins :: module
    r27 = 'print'
    r28 = CPyObject_GetAttr(r26, r27)
    r29 = [r25]
    r30 = load_address r29
    r31 = _PyObject_Vectorcall(r28, r30, 1, 0)
    keep_alive r25
    goto L6
L5:
L6:
    r32 = box(None, 1)
    return r32
[case testMatchClassPatternWithKeywordPatterns_python3_10]
class Position:
    x: int
    y: int
    z: int

def f(x):
    match x:
        case Position(z=1, y=2, x=3):
            print("matched")
[out]
def f(x):
    x, r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: str
    r5, r6, r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11 :: str
    r12, r13, r14 :: object
    r15 :: i32
    r16 :: bit
    r17 :: bool
    r18 :: str
    r19, r20, r21 :: object
    r22 :: i32
    r23 :: bit
    r24 :: bool
    r25 :: str
    r26 :: object
    r27 :: str
    r28 :: object
    r29 :: object[1]
    r30 :: object_ptr
    r31, r32 :: object
L0:
    r0 = __main__.Position :: type
    r1 = PyObject_IsInstance(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L5 :: bool
L1:
    r4 = 'z'
    r5 = CPyObject_GetAttr(x, r4)
    r6 = object 1
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L2 else goto L5 :: bool
L2:
    r11 = 'y'
    r12 = CPyObject_GetAttr(x, r11)
    r13 = object 2
    r14 = PyObject_RichCompare(r12, r13, 2)
    r15 = PyObject_IsTrue(r14)
    r16 = r15 >= 0 :: signed
    r17 = truncate r15: i32 to builtins.bool
    if r17 goto L3 else goto L5 :: bool
L3:
    r18 = 'x'
    r19 = CPyObject_GetAttr(x, r18)
    r20 = object 3
    r21 = PyObject_RichCompare(r19, r20, 2)
    r22 = PyObject_IsTrue(r21)
    r23 = r22 >= 0 :: signed
    r24 = truncate r22: i32 to builtins.bool
    if r24 goto L4 else goto L5 :: bool
L4:
    r25 = 'matched'
    r26 = builtins :: module
    r27 = 'print'
    r28 = CPyObject_GetAttr(r26, r27)
    r29 = [r25]
    r30 = load_address r29
    r31 = _PyObject_Vectorcall(r28, r30, 1, 0)
    keep_alive r25
    goto L6
L5:
L6:
    r32 = box(None, 1)
    return r32
[case testMatchClassPatternWithNestedPattern_python3_10]
class C:
    num: int

def f(x):
    match x:
        case C(num=1 | 2):
            print("matched")
[out]
def f(x):
    x, r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: str
    r5, r6, r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11, r12 :: object
    r13 :: i32
    r14 :: bit
    r15 :: bool
    r16 :: str
    r17 :: object
    r18 :: str
    r19 :: object
    r20 :: object[1]
    r21 :: object_ptr
    r22, r23 :: object
L0:
    r0 = __main__.C :: type
    r1 = PyObject_IsInstance(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L5 :: bool
L1:
    r4 = 'num'
    r5 = CPyObject_GetAttr(x, r4)
    r6 = object 1
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L4 else goto L2 :: bool
L2:
    r11 = object 2
    r12 = PyObject_RichCompare(r5, r11, 2)
    r13 = PyObject_IsTrue(r12)
    r14 = r13 >= 0 :: signed
    r15 = truncate r13: i32 to builtins.bool
    if r15 goto L4 else goto L3 :: bool
L3:
    goto L5
L4:
    r16 = 'matched'
    r17 = builtins :: module
    r18 = 'print'
    r19 = CPyObject_GetAttr(r17, r18)
    r20 = [r16]
    r21 = load_address r20
    r22 = _PyObject_Vectorcall(r19, r21, 1, 0)
    keep_alive r16
    goto L6
L5:
L6:
    r23 = box(None, 1)
    return r23
[case testAsPatternDoesntBleedIntoSubPatterns_python3_10]
class C:
    __match_args__ = ("a", "b")
    a: int
    b: int

def f(x):
    match x:
        case C(1, 2) as y:
            print("matched")
[out]
def C.__mypyc_defaults_setup(__mypyc_self__):
    __mypyc_self__ :: __main__.C
    r0, r1 :: str
    r2 :: tuple[str, str]
L0:
    r0 = 'a'
    r1 = 'b'
    r2 = (r0, r1)
    __mypyc_self__.__match_args__ = r2
    return 1
def f(x):
    x, r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4, y :: __main__.C
    r5 :: str
    r6, r7, r8 :: object
    r9 :: i32
    r10 :: bit
    r11 :: bool
    r12 :: str
    r13, r14, r15 :: object
    r16 :: i32
    r17 :: bit
    r18 :: bool
    r19 :: str
    r20 :: object
    r21 :: str
    r22 :: object
    r23 :: object[1]
    r24 :: object_ptr
    r25, r26 :: object
L0:
    r0 = __main__.C :: type
    r1 = PyObject_IsInstance(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L5 :: bool
L1:
    r4 = cast(__main__.C, x)
    y = r4
L2:
    r5 = 'a'
    r6 = CPyObject_GetAttr(x, r5)
    r7 = object 1
    r8 = PyObject_RichCompare(r6, r7, 2)
    r9 = PyObject_IsTrue(r8)
    r10 = r9 >= 0 :: signed
    r11 = truncate r9: i32 to builtins.bool
    if r11 goto L3 else goto L5 :: bool
L3:
    r12 = 'b'
    r13 = CPyObject_GetAttr(x, r12)
    r14 = object 2
    r15 = PyObject_RichCompare(r13, r14, 2)
    r16 = PyObject_IsTrue(r15)
    r17 = r16 >= 0 :: signed
    r18 = truncate r16: i32 to builtins.bool
    if r18 goto L4 else goto L5 :: bool
L4:
    r19 = 'matched'
    r20 = builtins :: module
    r21 = 'print'
    r22 = CPyObject_GetAttr(r20, r21)
    r23 = [r19]
    r24 = load_address r23
    r25 = _PyObject_Vectorcall(r22, r24, 1, 0)
    keep_alive r19
    goto L6
L5:
L6:
    r26 = box(None, 1)
    return r26
[case testMatchClassPatternPositionalCapture_python3_10]
class C:
    __match_args__ = ("x",)

    x: int

def f(x):
    match x:
        case C(num):
            print("matched")
[out]
def C.__mypyc_defaults_setup(__mypyc_self__):
    __mypyc_self__ :: __main__.C
    r0 :: str
    r1 :: tuple[str]
L0:
    r0 = 'x'
    r1 = (r0)
    __mypyc_self__.__match_args__ = r1
    return 1
def f(x):
    x, r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: str
    r5 :: object
    r6, num :: int
    r7 :: str
    r8 :: object
    r9 :: str
    r10 :: object
    r11 :: object[1]
    r12 :: object_ptr
    r13, r14 :: object
L0:
    r0 = __main__.C :: type
    r1 = PyObject_IsInstance(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L3 :: bool
L1:
    r4 = 'x'
    r5 = CPyObject_GetAttr(x, r4)
    r6 = unbox(int, r5)
    num = r6
L2:
    r7 = 'matched'
    r8 = builtins :: module
    r9 = 'print'
    r10 = CPyObject_GetAttr(r8, r9)
    r11 = [r7]
    r12 = load_address r11
    r13 = _PyObject_Vectorcall(r10, r12, 1, 0)
    keep_alive r7
    goto L4
L3:
L4:
    r14 = box(None, 1)
    return r14
[case testMatchMappingEmpty_python3_10]
def f(x):
    match x:
        case {}:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: str
    r3 :: object
    r4 :: str
    r5 :: object
    r6 :: object[1]
    r7 :: object_ptr
    r8, r9 :: object
L0:
    r0 = CPyMapping_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L2 :: bool
L1:
    r2 = 'matched'
    r3 = builtins :: module
    r4 = 'print'
    r5 = CPyObject_GetAttr(r3, r4)
    r6 = [r2]
    r7 = load_address r6
    r8 = _PyObject_Vectorcall(r5, r7, 1, 0)
    keep_alive r2
    goto L3
L2:
L3:
    r9 = box(None, 1)
    return r9
[case testMatchMappingPatternWithKeys_python3_10]
def f(x):
    match x:
        case {"key": "value"}:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: str
    r3 :: i32
    r4 :: bit
    r5 :: object
    r6 :: str
    r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11 :: str
    r12 :: object
    r13 :: str
    r14 :: object
    r15 :: object[1]
    r16 :: object_ptr
    r17, r18 :: object
L0:
    r0 = CPyMapping_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L4 :: bool
L1:
    r2 = 'key'
    r3 = PyMapping_HasKey(x, r2)
    r4 = r3 != 0
    if r4 goto L2 else goto L4 :: bool
L2:
    r5 = PyObject_GetItem(x, r2)
    r6 = 'value'
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L3 else goto L4 :: bool
L3:
    r11 = 'matched'
    r12 = builtins :: module
    r13 = 'print'
    r14 = CPyObject_GetAttr(r12, r13)
    r15 = [r11]
    r16 = load_address r15
    r17 = _PyObject_Vectorcall(r14, r16, 1, 0)
    keep_alive r11
    goto L5
L4:
L5:
    r18 = box(None, 1)
    return r18
[case testMatchMappingPatternWithRest_python3_10]
def f(x):
    match x:
        case {**rest}:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2, rest :: dict
    r3 :: str
    r4 :: object
    r5 :: str
    r6 :: object
    r7 :: object[1]
    r8 :: object_ptr
    r9, r10 :: object
L0:
    r0 = CPyMapping_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L3 :: bool
L1:
    r2 = CPyDict_FromAny(x)
    rest = r2
L2:
    r3 = 'matched'
    r4 = builtins :: module
    r5 = 'print'
    r6 = CPyObject_GetAttr(r4, r5)
    r7 = [r3]
    r8 = load_address r7
    r9 = _PyObject_Vectorcall(r6, r8, 1, 0)
    keep_alive r3
    goto L4
L3:
L4:
    r10 = box(None, 1)
    return r10
[case testMatchMappingPatternWithRestPopKeys_python3_10]
def f(x):
    match x:
        case {"key": "value", **rest}:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: str
    r3 :: i32
    r4 :: bit
    r5 :: object
    r6 :: str
    r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11, rest :: dict
    r12 :: i32
    r13 :: bit
    r14 :: str
    r15 :: object
    r16 :: str
    r17 :: object
    r18 :: object[1]
    r19 :: object_ptr
    r20, r21 :: object
L0:
    r0 = CPyMapping_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L5 :: bool
L1:
    r2 = 'key'
    r3 = PyMapping_HasKey(x, r2)
    r4 = r3 != 0
    if r4 goto L2 else goto L5 :: bool
L2:
    r5 = PyObject_GetItem(x, r2)
    r6 = 'value'
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L3 else goto L5 :: bool
L3:
    r11 = CPyDict_FromAny(x)
    rest = r11
    r12 = PyDict_DelItem(r11, r2)
    r13 = r12 >= 0 :: signed
L4:
    r14 = 'matched'
    r15 = builtins :: module
    r16 = 'print'
    r17 = CPyObject_GetAttr(r15, r16)
    r18 = [r14]
    r19 = load_address r18
    r20 = _PyObject_Vectorcall(r17, r19, 1, 0)
    keep_alive r14
    goto L6
L5:
L6:
    r21 = box(None, 1)
    return r21
[case testMatchEmptySequencePattern_python3_10]
def f(x):
    match x:
        case []:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5 :: str
    r6 :: object
    r7 :: str
    r8 :: object
    r9 :: object[1]
    r10 :: object_ptr
    r11, r12 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L3 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 == 0
    if r4 goto L2 else goto L3 :: bool
L2:
    r5 = 'matched'
    r6 = builtins :: module
    r7 = 'print'
    r8 = CPyObject_GetAttr(r6, r7)
    r9 = [r5]
    r10 = load_address r9
    r11 = _PyObject_Vectorcall(r8, r10, 1, 0)
    keep_alive r5
    goto L4
L3:
L4:
    r12 = box(None, 1)
    return r12
[case testMatchFixedLengthSequencePattern_python3_10]
def f(x):
    match x:
        case [1, 2]:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5, r6, r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11, r12, r13 :: object
    r14 :: i32
    r15 :: bit
    r16 :: bool
    r17 :: str
    r18 :: object
    r19 :: str
    r20 :: object
    r21 :: object[1]
    r22 :: object_ptr
    r23, r24 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L5 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 == 2
    if r4 goto L2 else goto L5 :: bool
L2:
    r5 = PySequence_GetItem(x, 0)
    r6 = object 1
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L3 else goto L5 :: bool
L3:
    r11 = PySequence_GetItem(x, 1)
    r12 = object 2
    r13 = PyObject_RichCompare(r11, r12, 2)
    r14 = PyObject_IsTrue(r13)
    r15 = r14 >= 0 :: signed
    r16 = truncate r14: i32 to builtins.bool
    if r16 goto L4 else goto L5 :: bool
L4:
    r17 = 'matched'
    r18 = builtins :: module
    r19 = 'print'
    r20 = CPyObject_GetAttr(r18, r19)
    r21 = [r17]
    r22 = load_address r21
    r23 = _PyObject_Vectorcall(r20, r22, 1, 0)
    keep_alive r17
    goto L6
L5:
L6:
    r24 = box(None, 1)
    return r24
[case testMatchSequencePatternWithTrailingUnboundStar_python3_10]
def f(x):
    match x:
        case [1, 2, *_]:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5, r6, r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11, r12, r13 :: object
    r14 :: i32
    r15 :: bit
    r16 :: bool
    r17 :: str
    r18 :: object
    r19 :: str
    r20 :: object
    r21 :: object[1]
    r22 :: object_ptr
    r23, r24 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L5 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 >= 2 :: signed
    if r4 goto L2 else goto L5 :: bool
L2:
    r5 = PySequence_GetItem(x, 0)
    r6 = object 1
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L3 else goto L5 :: bool
L3:
    r11 = PySequence_GetItem(x, 1)
    r12 = object 2
    r13 = PyObject_RichCompare(r11, r12, 2)
    r14 = PyObject_IsTrue(r13)
    r15 = r14 >= 0 :: signed
    r16 = truncate r14: i32 to builtins.bool
    if r16 goto L4 else goto L5 :: bool
L4:
    r17 = 'matched'
    r18 = builtins :: module
    r19 = 'print'
    r20 = CPyObject_GetAttr(r18, r19)
    r21 = [r17]
    r22 = load_address r21
    r23 = _PyObject_Vectorcall(r20, r22, 1, 0)
    keep_alive r17
    goto L6
L5:
L6:
    r24 = box(None, 1)
    return r24
[case testMatchSequencePatternWithTrailingBoundStar_python3_10]
def f(x):
    match x:
        case [1, 2, *rest]:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5, r6, r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11, r12, r13 :: object
    r14 :: i32
    r15 :: bit
    r16 :: bool
    r17 :: native_int
    r18, rest :: object
    r19 :: str
    r20 :: object
    r21 :: str
    r22 :: object
    r23 :: object[1]
    r24 :: object_ptr
    r25, r26 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L6 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 >= 2 :: signed
    if r4 goto L2 else goto L6 :: bool
L2:
    r5 = PySequence_GetItem(x, 0)
    r6 = object 1
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L3 else goto L6 :: bool
L3:
    r11 = PySequence_GetItem(x, 1)
    r12 = object 2
    r13 = PyObject_RichCompare(r11, r12, 2)
    r14 = PyObject_IsTrue(r13)
    r15 = r14 >= 0 :: signed
    r16 = truncate r14: i32 to builtins.bool
    if r16 goto L4 else goto L6 :: bool
L4:
    r17 = r2 - 0
    r18 = PySequence_GetSlice(x, 2, r17)
    rest = r18
L5:
    r19 = 'matched'
    r20 = builtins :: module
    r21 = 'print'
    r22 = CPyObject_GetAttr(r20, r21)
    r23 = [r19]
    r24 = load_address r23
    r25 = _PyObject_Vectorcall(r22, r24, 1, 0)
    keep_alive r19
    goto L7
L6:
L7:
    r26 = box(None, 1)
    return r26
[case testMatchSequenceWithStarPatternInTheMiddle_python3_10]
def f(x):
    match x:
        case ["start", *rest, "end"]:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5 :: object
    r6 :: str
    r7 :: object
    r8 :: i32
    r9 :: bit
    r10 :: bool
    r11 :: native_int
    r12 :: object
    r13 :: str
    r14 :: object
    r15 :: i32
    r16 :: bit
    r17 :: bool
    r18 :: native_int
    r19, rest :: object
    r20 :: str
    r21 :: object
    r22 :: str
    r23 :: object
    r24 :: object[1]
    r25 :: object_ptr
    r26, r27 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L6 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 >= 2 :: signed
    if r4 goto L2 else goto L6 :: bool
L2:
    r5 = PySequence_GetItem(x, 0)
    r6 = 'start'
    r7 = PyObject_RichCompare(r5, r6, 2)
    r8 = PyObject_IsTrue(r7)
    r9 = r8 >= 0 :: signed
    r10 = truncate r8: i32 to builtins.bool
    if r10 goto L3 else goto L6 :: bool
L3:
    r11 = r2 - 1
    r12 = PySequence_GetItem(x, r11)
    r13 = 'end'
    r14 = PyObject_RichCompare(r12, r13, 2)
    r15 = PyObject_IsTrue(r14)
    r16 = r15 >= 0 :: signed
    r17 = truncate r15: i32 to builtins.bool
    if r17 goto L4 else goto L6 :: bool
L4:
    r18 = r2 - 1
    r19 = PySequence_GetSlice(x, 1, r18)
    rest = r19
L5:
    r20 = 'matched'
    r21 = builtins :: module
    r22 = 'print'
    r23 = CPyObject_GetAttr(r21, r22)
    r24 = [r20]
    r25 = load_address r24
    r26 = _PyObject_Vectorcall(r23, r25, 1, 0)
    keep_alive r20
    goto L7
L6:
L7:
    r27 = box(None, 1)
    return r27
[case testMatchSequenceWithStarPatternAtTheStart_python3_10]
def f(x):
    match x:
        case [*rest, 1, 2]:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5 :: native_int
    r6, r7, r8 :: object
    r9 :: i32
    r10 :: bit
    r11 :: bool
    r12 :: native_int
    r13, r14, r15 :: object
    r16 :: i32
    r17 :: bit
    r18 :: bool
    r19 :: native_int
    r20, rest :: object
    r21 :: str
    r22 :: object
    r23 :: str
    r24 :: object
    r25 :: object[1]
    r26 :: object_ptr
    r27, r28 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L6 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 >= 2 :: signed
    if r4 goto L2 else goto L6 :: bool
L2:
    r5 = r2 - 2
    r6 = PySequence_GetItem(x, r5)
    r7 = object 1
    r8 = PyObject_RichCompare(r6, r7, 2)
    r9 = PyObject_IsTrue(r8)
    r10 = r9 >= 0 :: signed
    r11 = truncate r9: i32 to builtins.bool
    if r11 goto L3 else goto L6 :: bool
L3:
    r12 = r2 - 1
    r13 = PySequence_GetItem(x, r12)
    r14 = object 2
    r15 = PyObject_RichCompare(r13, r14, 2)
    r16 = PyObject_IsTrue(r15)
    r17 = r16 >= 0 :: signed
    r18 = truncate r16: i32 to builtins.bool
    if r18 goto L4 else goto L6 :: bool
L4:
    r19 = r2 - 2
    r20 = PySequence_GetSlice(x, 0, r19)
    rest = r20
L5:
    r21 = 'matched'
    r22 = builtins :: module
    r23 = 'print'
    r24 = CPyObject_GetAttr(r22, r23)
    r25 = [r21]
    r26 = load_address r25
    r27 = _PyObject_Vectorcall(r24, r26, 1, 0)
    keep_alive r21
    goto L7
L6:
L7:
    r28 = box(None, 1)
    return r28
[case testMatchBuiltinClassPattern_python3_10]
def f(x):
    match x:
        case int(y):
            print("matched")
[out]
def f(x):
    x, r0 :: object
    r1 :: bool
    r2, y :: int
    r3 :: str
    r4 :: object
    r5 :: str
    r6 :: object
    r7 :: object[1]
    r8 :: object_ptr
    r9, r10 :: object
L0:
    r0 = load_address PyLong_Type
    r1 = CPy_TypeCheck(x, r0)
    if r1 goto L1 else goto L3 :: bool
L1:
    r2 = unbox(int, x)
    y = r2
L2:
    r3 = 'matched'
    r4 = builtins :: module
    r5 = 'print'
    r6 = CPyObject_GetAttr(r4, r5)
    r7 = [r3]
    r8 = load_address r7
    r9 = _PyObject_Vectorcall(r6, r8, 1, 0)
    keep_alive r3
    goto L4
L3:
L4:
    r10 = box(None, 1)
    return r10
[case testMatchSequenceCaptureAll_python3_10]
def f(x):
    match x:
        case [*rest]:
            print("matched")
[out]
def f(x):
    x :: object
    r0 :: i32
    r1 :: bit
    r2 :: native_int
    r3, r4 :: bit
    r5 :: native_int
    r6, rest :: object
    r7 :: str
    r8 :: object
    r9 :: str
    r10 :: object
    r11 :: object[1]
    r12 :: object_ptr
    r13, r14 :: object
L0:
    r0 = CPySequence_Check(x)
    r1 = r0 != 0
    if r1 goto L1 else goto L4 :: bool
L1:
    r2 = PyObject_Size(x)
    r3 = r2 >= 0 :: signed
    r4 = r2 >= 0 :: signed
    if r4 goto L2 else goto L4 :: bool
L2:
    r5 = r2 - 0
    r6 = PySequence_GetSlice(x, 0, r5)
    rest = r6
L3:
    r7 = 'matched'
    r8 = builtins :: module
    r9 = 'print'
    r10 = CPyObject_GetAttr(r8, r9)
    r11 = [r7]
    r12 = load_address r11
    r13 = _PyObject_Vectorcall(r10, r12, 1, 0)
    keep_alive r7
    goto L5
L4:
L5:
    r14 = box(None, 1)
    return r14
[case testMatchTypeAnnotatedNativeClass_python3_10]
class A:
    a: int

def f(x: A | int) -> int:
    match x:
        case A(a=a):
            return a
        case int():
            return x
[out]
def f(x):
    x :: union[__main__.A, int]
    r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: str
    r5 :: object
    r6, a :: int
    r7 :: object
    r8 :: bool
    r9 :: int
L0:
    r0 = __main__.A :: type
    r1 = PyObject_IsInstance(x, r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L3 :: bool
L1:
    r4 = 'a'
    r5 = CPyObject_GetAttr(x, r4)
    r6 = unbox(int, r5)
    a = r6
L2:
    return a
L3:
    r7 = load_address PyLong_Type
    r8 = CPy_TypeCheck(x, r7)
    if r8 goto L4 else goto L5 :: bool
L4:
    r9 = unbox(int, x)
    return r9
L5:
L6:
    unreachable

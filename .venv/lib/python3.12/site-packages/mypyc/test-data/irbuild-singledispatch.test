[case testNativeCallsUsedInDispatchFunction]
from functools import singledispatch
@singledispatch
def f(arg) -> bool:
    return False

@f.register
def g(arg: int) -> bool:
    return True
[out]
def __mypyc_singledispatch_main_function_f__(arg):
    arg :: object
L0:
    return 0
def f_obj.__init__(__mypyc_self__):
    __mypyc_self__ :: __main__.f_obj
    r0, r1 :: dict
    r2 :: str
    r3 :: i32
    r4 :: bit
L0:
    r0 = PyDict_New()
    __mypyc_self__.registry = r0
    r1 = PyDict_New()
    r2 = 'dispatch_cache'
    r3 = PyObject_SetAttr(__mypyc_self__, r2, r1)
    r4 = r3 >= 0 :: signed
    return 1
def f_obj.__call__(__mypyc_self__, arg):
    __mypyc_self__ :: __main__.f_obj
    arg :: object
    r0 :: ptr
    r1 :: object
    r2 :: dict
    r3, r4 :: object
    r5 :: bit
    r6, r7 :: object
    r8 :: str
    r9 :: object
    r10 :: dict
    r11 :: object
    r12 :: i32
    r13 :: bit
    r14 :: object
    r15 :: ptr
    r16 :: object
    r17 :: bit
    r18 :: int
    r19 :: bit
    r20 :: int
    r21 :: bool
    r22 :: object
    r23 :: bool
L0:
    r0 = get_element_ptr arg ob_type :: PyObject
    r1 = load_mem r0 :: builtins.object*
    keep_alive arg
    r2 = __mypyc_self__.dispatch_cache
    r3 = CPyDict_GetWithNone(r2, r1)
    r4 = load_address _Py_NoneStruct
    r5 = r3 != r4
    if r5 goto L1 else goto L2 :: bool
L1:
    r6 = r3
    goto L3
L2:
    r7 = functools :: module
    r8 = '_find_impl'
    r9 = CPyObject_GetAttr(r7, r8)
    r10 = __mypyc_self__.registry
    r11 = PyObject_CallFunctionObjArgs(r9, r1, r10, 0)
    r12 = CPyDict_SetItem(r2, r1, r11)
    r13 = r12 >= 0 :: signed
    r6 = r11
L3:
    r14 = load_address PyLong_Type
    r15 = get_element_ptr r6 ob_type :: PyObject
    r16 = load_mem r15 :: builtins.object*
    keep_alive r6
    r17 = r16 == r14
    if r17 goto L4 else goto L7 :: bool
L4:
    r18 = unbox(int, r6)
    r19 = r18 == 0
    if r19 goto L5 else goto L6 :: bool
L5:
    r20 = unbox(int, arg)
    r21 = g(r20)
    return r21
L6:
    unreachable
L7:
    r22 = PyObject_CallFunctionObjArgs(r6, arg, 0)
    r23 = unbox(bool, r22)
    return r23
def f_obj.__get__(__mypyc_self__, instance, owner):
    __mypyc_self__, instance, owner, r0 :: object
    r1 :: bit
    r2 :: object
L0:
    r0 = load_address _Py_NoneStruct
    r1 = instance == r0
    if r1 goto L1 else goto L2 :: bool
L1:
    return __mypyc_self__
L2:
    r2 = PyMethod_New(__mypyc_self__, instance)
    return r2
def f_obj.register(__mypyc_self__, cls, func):
    __mypyc_self__ :: __main__.f_obj
    cls, func, r0 :: object
L0:
    r0 = CPySingledispatch_RegisterFunction(__mypyc_self__, cls, func)
    return r0
def f(arg):
    arg :: object
    r0 :: dict
    r1 :: str
    r2 :: object
    r3 :: bool
L0:
    r0 = __main__.globals :: static
    r1 = 'f'
    r2 = CPyDict_GetItem(r0, r1)
    r3 = f_obj.__call__(r2, arg)
    return r3
def g(arg):
    arg :: int
L0:
    return 1


[case testCallsToSingledispatchFunctionsAreNative]
from functools import singledispatch

@singledispatch
def f(x: object) -> None:
    pass

def test():
    f('a')
[out]
def __mypyc_singledispatch_main_function_f__(x):
    x :: object
L0:
    return 1
def f_obj.__init__(__mypyc_self__):
    __mypyc_self__ :: __main__.f_obj
    r0, r1 :: dict
    r2 :: str
    r3 :: i32
    r4 :: bit
L0:
    r0 = PyDict_New()
    __mypyc_self__.registry = r0
    r1 = PyDict_New()
    r2 = 'dispatch_cache'
    r3 = PyObject_SetAttr(__mypyc_self__, r2, r1)
    r4 = r3 >= 0 :: signed
    return 1
def f_obj.__call__(__mypyc_self__, x):
    __mypyc_self__ :: __main__.f_obj
    x :: object
    r0 :: ptr
    r1 :: object
    r2 :: dict
    r3, r4 :: object
    r5 :: bit
    r6, r7 :: object
    r8 :: str
    r9 :: object
    r10 :: dict
    r11 :: object
    r12 :: i32
    r13 :: bit
    r14 :: object
    r15 :: ptr
    r16 :: object
    r17 :: bit
    r18 :: int
    r19 :: object
    r20 :: None
L0:
    r0 = get_element_ptr x ob_type :: PyObject
    r1 = load_mem r0 :: builtins.object*
    keep_alive x
    r2 = __mypyc_self__.dispatch_cache
    r3 = CPyDict_GetWithNone(r2, r1)
    r4 = load_address _Py_NoneStruct
    r5 = r3 != r4
    if r5 goto L1 else goto L2 :: bool
L1:
    r6 = r3
    goto L3
L2:
    r7 = functools :: module
    r8 = '_find_impl'
    r9 = CPyObject_GetAttr(r7, r8)
    r10 = __mypyc_self__.registry
    r11 = PyObject_CallFunctionObjArgs(r9, r1, r10, 0)
    r12 = CPyDict_SetItem(r2, r1, r11)
    r13 = r12 >= 0 :: signed
    r6 = r11
L3:
    r14 = load_address PyLong_Type
    r15 = get_element_ptr r6 ob_type :: PyObject
    r16 = load_mem r15 :: builtins.object*
    keep_alive r6
    r17 = r16 == r14
    if r17 goto L4 else goto L5 :: bool
L4:
    r18 = unbox(int, r6)
    unreachable
L5:
    r19 = PyObject_CallFunctionObjArgs(r6, x, 0)
    r20 = unbox(None, r19)
    return r20
def f_obj.__get__(__mypyc_self__, instance, owner):
    __mypyc_self__, instance, owner, r0 :: object
    r1 :: bit
    r2 :: object
L0:
    r0 = load_address _Py_NoneStruct
    r1 = instance == r0
    if r1 goto L1 else goto L2 :: bool
L1:
    return __mypyc_self__
L2:
    r2 = PyMethod_New(__mypyc_self__, instance)
    return r2
def f_obj.register(__mypyc_self__, cls, func):
    __mypyc_self__ :: __main__.f_obj
    cls, func, r0 :: object
L0:
    r0 = CPySingledispatch_RegisterFunction(__mypyc_self__, cls, func)
    return r0
def f(x):
    x :: object
    r0 :: dict
    r1 :: str
    r2 :: object
    r3 :: None
L0:
    r0 = __main__.globals :: static
    r1 = 'f'
    r2 = CPyDict_GetItem(r0, r1)
    r3 = f_obj.__call__(r2, x)
    return r3
def test():
    r0 :: str
    r1 :: None
    r2 :: object
L0:
    r0 = 'a'
    r1 = f(r0)
    r2 = box(None, 1)
    return r2

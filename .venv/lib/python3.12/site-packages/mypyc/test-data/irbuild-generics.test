[case testGenericFunction]
from typing import TypeVar, List
T = TypeVar('T')
def f(x: T) -> T:
    return x
def g(x: List[T]) -> List[T]:
    return [x[0]]
def h(x: int, y: List[int]) -> None:
    x = f(x)
    y = g(y)
[out]
def f(x):
    x :: object
L0:
    return x
def g(x):
    x :: list
    r0 :: object
    r1 :: list
    r2, r3 :: ptr
L0:
    r0 = CPyList_GetItemShort(x, 0)
    r1 = PyList_New(1)
    r2 = get_element_ptr r1 ob_item :: PyListObject
    r3 = load_mem r2 :: ptr*
    set_mem r3, r0 :: builtins.object*
    keep_alive r1
    return r1
def h(x, y):
    x :: int
    y :: list
    r0, r1 :: object
    r2 :: int
    r3 :: list
L0:
    r0 = box(int, x)
    r1 = f(r0)
    r2 = unbox(int, r1)
    x = r2
    r3 = g(y)
    y = r3
    return 1

[case testGenericAttrAndTypeApplication]
from typing import TypeVar, Generic
T = TypeVar('T')
class C(Generic[T]):
    x: T
def f() -> None:
    c = C[int]()
    c.x = 1
    2 + c.x
[out]
def f():
    r0, c :: __main__.C
    r1 :: object
    r2 :: bool
    r3 :: object
    r4, r5 :: int
L0:
    r0 = C()
    c = r0
    r1 = object 1
    c.x = r1; r2 = is_error
    r3 = borrow c.x
    r4 = unbox(int, r3)
    r5 = CPyTagged_Add(4, r4)
    keep_alive c
    return 1

[case testGenericMethod]
from typing import TypeVar, Generic
T = TypeVar('T')
class C(Generic[T]):
    x: T
    def __init__(self, x: T) -> None:
        self.x = x
    def get(self) -> T:
        return self.x
    def set(self, y: T) -> None:
        self.x = y
def f(x: C[int]) -> None:
    y = x.get()
    x.set(y + 1)
    x = C(2)
[out]
def C.__init__(self, x):
    self :: __main__.C
    x :: object
    r0 :: bool
L0:
    self.x = x; r0 = is_error
    return 1
def C.get(self):
    self :: __main__.C
    r0 :: object
L0:
    r0 = self.x
    return r0
def C.set(self, y):
    self :: __main__.C
    y :: object
    r0 :: bool
L0:
    self.x = y; r0 = is_error
    return 1
def f(x):
    x :: __main__.C
    r0 :: object
    r1, y, r2 :: int
    r3 :: object
    r4 :: None
    r5 :: object
    r6 :: __main__.C
L0:
    r0 = x.get()
    r1 = unbox(int, r0)
    y = r1
    r2 = CPyTagged_Add(y, 2)
    r3 = box(int, r2)
    r4 = x.set(r3)
    r5 = object 2
    r6 = C(r5)
    x = r6
    return 1

[case testMax]
from typing import TypeVar
T = TypeVar('T')
def f(x: T, y: T) -> T:
    return max(x, y)
[out]
def f(x, y):
    x, y, r0 :: object
    r1 :: i32
    r2 :: bit
    r3 :: bool
    r4 :: object
L0:
    r0 = PyObject_RichCompare(y, x, 4)
    r1 = PyObject_IsTrue(r0)
    r2 = r1 >= 0 :: signed
    r3 = truncate r1: i32 to builtins.bool
    if r3 goto L1 else goto L2 :: bool
L1:
    r4 = y
    goto L3
L2:
    r4 = x
L3:
    return r4

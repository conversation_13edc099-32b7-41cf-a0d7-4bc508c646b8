-- Test cases for Python 3.7 features

[case testRunDataclass]
import dataclasses
from dataclasses import dataclass, field
from typing import Set, FrozenSet, List, Callable, Any

@dataclass
class Person1:
    age : int
    name : str

    def __bool__(self) -> bool:
        return self.name == 'robot'

def testBool(p: Person1) -> bool:
    if p:
        return True
    else:
        return False

@dataclass
class Person1b(Person1):
    id: str = '000'

@dataclass
class Person2:
    age : int
    name : str = field(default='robot')

@dataclasses.dataclass
class Person2b:
    age : int
    name : str = dataclasses.field(default='robot')

@dataclass(order = True)
class Person3:
    age : int = field(default = 6)
    friendIDs : List[int] = field(default_factory = list)

    def get_age(self) -> int:
        return (self.age)

    def set_age(self, new_age : int) -> None:
        self.age = new_age

    def add_friendID(self, fid : int) -> None:
        self.friendIDs.append(fid)

    def get_friendIDs(self) -> List[int]:
        return self.friendIDs

def get_next_age(g: Callable[[Any], int]) -> Callable[[Any], int]:
    def f(a: Any) -> int:
        return g(a) + 1
    return f

@dataclass
class Person4:
    age : int
    _name : str = 'Bot'

    @get_next_age
    def get_age(self) -> int:
        return self.age

    @property
    def name(self) -> str:
        return self._name

@dataclass
class Person5:
    weight: float
    friends: Set[str] = field(default_factory=set)
    parents: FrozenSet[str] = frozenset()

[file other.py]
from native import Person1, Person1b, Person2, Person3, Person4, Person5, testBool
i1 = Person1(age = 5, name = 'robot')
assert i1.age == 5
assert i1.name == 'robot'
assert testBool(i1) == True
assert testBool(Person1(age = 5, name = 'robo')) == False
i1b = Person1b(age = 5, name = 'robot')
assert i1b.age == 5
assert i1b.name == 'robot'
assert testBool(i1b) == True
assert testBool(Person1b(age = 5, name = 'robo')) == False
i1c = Person1b(age = 20, name = 'robot', id = 'test')
assert i1c.age == 20
assert i1c.id == 'test'

i2 = Person2(age = 5)
assert i2.age == 5
assert i2.name == 'robot'
i3 = Person2(age = 5, name = 'new_robot')
assert i3.age == 5
assert i3.name == 'new_robot'
i4 = Person3()
assert i4.age == 6
assert i4.friendIDs == []
i5 = Person3(age = 5)
assert i5.age == 5
assert i5.friendIDs == []
i6 = Person3(age = 5, friendIDs = [1,2,3])
assert i6.age == 5
assert i6.friendIDs == [1,2,3]
assert i6.get_age() == 5
i6.set_age(10)
assert i6.get_age() == 10
i6.add_friendID(4)
assert i6.get_friendIDs() == [1,2,3,4]
i7 = Person4(age = 5)
assert i7.get_age() == 6
i7.age += 3
assert i7.age == 8
assert i7.name == 'Bot'
i8 = Person3(age = 1, friendIDs = [1,2])
i9 = Person3(age = 1, friendIDs = [1,2])
assert i8 == i9
i8.age = 2
assert i8 > i9

assert Person1.__annotations__ == {'age': int, 'name': str}
assert Person2.__annotations__ == {'age': int, 'name': str}
assert Person5.__annotations__ == {'weight': float, 'friends': set,
                                       'parents': frozenset}

[file driver.py]
import sys

# Dataclasses introduced in 3.7
version = sys.version_info[:2]
if version[0] < 3 or version[1] < 7:
    exit()

# Run the tests in both interpreted and compiled mode
import other
import other_interpreted

# Test for an exceptional cases
from testutil import assertRaises
from native import Person1, Person1b, Person3
from types import BuiltinMethodType

with assertRaises(TypeError, "missing 1 required positional argument"):
    Person1(0)

with assertRaises(TypeError, "missing 2 required positional arguments"):
    Person1b()

with assertRaises(TypeError, "int object expected; got str"):
    Person1('nope', 'test')

p = Person1(0, 'test')
with assertRaises(TypeError, "int object expected; got str"):
    p.age = 'nope'

assert isinstance(Person3().get_age, BuiltinMethodType)

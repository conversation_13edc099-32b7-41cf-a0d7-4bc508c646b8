#!/usr/bin/env python3
"""
测试项目设置脚本
"""
import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查 Python 版本"""
    print("检查 Python 版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print(f"❌ Python 版本过低: {version.major}.{version.minor}")
        print("需要 Python 3.11 或更高版本")
        return False
    print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_dependencies():
    """检查依赖是否安装"""
    print("\n检查依赖...")
    required_packages = [
        'flask',
        'sqlalchemy',
        'kubernetes',
        'redis',
        'psycopg2'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True


def check_project_structure():
    """检查项目结构"""
    print("\n检查项目结构...")
    
    required_files = [
        'k8s_manager.py',
        'requirements.txt',
        'docker-compose.yml',
        'Dockerfile',
        '.env.example',
        'README.md'
    ]
    
    required_dirs = [
        'app',
        'app/models',
        'app/services',
        'app/api',
        'app/auth',
        'app/cluster',
        'app/application',
        'app/monitoring',
        'app/security',
        'app/resource',
        'app/main',
        'app/errors'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/")
            missing_dirs.append(dir_path)
    
    if missing_files or missing_dirs:
        print(f"\n缺少文件: {missing_files}")
        print(f"缺少目录: {missing_dirs}")
        return False
    
    return True


def test_import():
    """测试导入"""
    print("\n测试模块导入...")
    
    try:
        from app import create_app
        print("✅ 应用工厂导入成功")
        
        app = create_app('testing')
        print("✅ 应用创建成功")
        
        with app.app_context():
            from app.models.user import User
            from app.models.cluster import Cluster
            from app.services.kubernetes_service import KubernetesService
            print("✅ 模型和服务导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def check_docker():
    """检查 Docker"""
    print("\n检查 Docker...")
    
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker: {result.stdout.strip()}")
        else:
            print("❌ Docker 未安装或不可用")
            return False
            
        result = subprocess.run(['docker-compose', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker Compose: {result.stdout.strip()}")
        else:
            print("❌ Docker Compose 未安装或不可用")
            return False
            
        return True
        
    except FileNotFoundError:
        print("❌ Docker 未安装")
        return False


def main():
    """主函数"""
    print("🚀 K8s Manager Platform 项目设置检查")
    print("=" * 50)
    
    checks = [
        ("Python 版本", check_python_version),
        ("项目结构", check_project_structure),
        ("依赖包", check_dependencies),
        ("模块导入", test_import),
        ("Docker", check_docker)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 检查失败: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("📊 检查结果汇总:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！项目设置正确。")
        print("\n下一步:")
        print("1. 复制 .env.example 到 .env 并配置")
        print("2. 启动服务: docker-compose up -d")
        print("3. 初始化数据库: flask init-db && flask init-data")
        print("4. 访问应用: http://localhost:5000")
    else:
        print("⚠️  部分检查失败，请修复后重试。")
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())

# Flask 配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 数据库配置
DATABASE_URL=postgresql://k8s_user:k8s_password@localhost/k8s_manager
DEV_DATABASE_URL=postgresql://k8s_user:k8s_password@localhost/k8s_manager_dev
TEST_DATABASE_URL=postgresql://k8s_user:k8s_password@localhost/k8s_manager_test

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# Kubernetes 配置
KUBECONFIG_PATH=~/.kube/config

# 监控配置
PROMETHEUS_URL=http://localhost:9090
GRAFANA_URL=http://localhost:3000
ELASTICSEARCH_URL=http://localhost:9200

# 邮件配置
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# LDAP 配置
LDAP_HOST=ldap.example.com
LDAP_PORT=389
LDAP_USE_SSL=false
LDAP_BASE_DN=dc=example,dc=com
LDAP_USER_DN=ou=users,dc=example,dc=com
LDAP_GROUP_DN=ou=groups,dc=example,dc=com

# 镜像仓库配置
HARBOR_URL=https://harbor.example.com
HARBOR_USERNAME=admin
HARBOR_PASSWORD=Harbor12345

# 安全配置
TRIVY_CACHE_DIR=/tmp/trivy-cache

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/k8s-manager.log

# 文件上传配置
UPLOAD_FOLDER=uploads

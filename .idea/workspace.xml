<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="45dc5810-461b-4efc-b0d2-90484f67d16b" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2zAjatKIyu3sx7mUxVeKlfO9R25" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ChatTagsLlmMigration": "true",
    "RunOnceActivity.ClientConfigCleanupMigration": "true",
    "RunOnceActivity.CodyAccountHistoryMigration": "true",
    "RunOnceActivity.CodyAccountsIdsRefresh": "true",
    "RunOnceActivity.CodyAssignOrphanedChatsToActiveAccount": "true",
    "RunOnceActivity.CodyConvertUrlToCodebaseName": "true",
    "RunOnceActivity.CodyHistoryLlmMigration": "true",
    "RunOnceActivity.CodyMigrateChatHistory-v2": "true",
    "RunOnceActivity.CodyProjectSettingsMigration": "true",
    "RunOnceActivity.DeprecatedChatLlmMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.ToggleCodyToolWindowAfterMigration": "true",
    "last_opened_file_path": "/Users/<USER>/augment/python-k8s-manager"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-495700d161d3-aa17d162503b-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-243.22562.220" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="45dc5810-461b-4efc-b0d2-90484f67d16b" name="Changes" comment="" />
      <created>1751180838919</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751180838919</updated>
    </task>
    <servers />
  </component>
</project>